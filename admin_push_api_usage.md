# 后台管理员推送API使用说明

## 概述

`AdminPush` 控制器专门用于向后台管理员推送实时通知消息，支持多种推送方式和消息类型。

## API接口列表

### 1. 推送消息 - `/api/admin_push/pushMessage`

**请求方式**: POST

**参数说明**:
- `title` (string, 必填): 通知标题
- `content` (string, 必填): 通知内容  
- `type` (string, 可选): 通知类型，可选值：system, success, error, warning, info，默认为system
- `url` (string, 可选): 点击通知跳转的URL
- `icon` (integer, 可选): 通知图标，0-默认，1-成功，2-错误，3-警告，4-信息，默认为0
- `method` (string, 可选): 推送方式，可选值：websocket, redis, both，默认为both

**请求示例**:
```bash
curl -X POST "http://your-domain.com/api/admin_push/pushMessage" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "系统通知",
    "content": "系统运行正常，所有服务已启动。",
    "type": "system",
    "method": "both"
  }'
```

### 2. 推送系统通知 - `/api/admin_push/pushSystemNotice`

**请求方式**: POST

**参数说明**:
- `title` (string, 可选): 通知标题，默认为"系统通知"
- `content` (string, 必填): 通知内容

**请求示例**:
```bash
curl -X POST "http://your-domain.com/api/admin_push/pushSystemNotice" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "系统维护已完成，所有功能恢复正常。"
  }'
```

### 3. 推送警告消息 - `/api/admin_push/pushWarning`

**请求方式**: POST

**参数说明**:
- `title` (string, 可选): 通知标题，默认为"警告提醒"
- `content` (string, 必填): 通知内容

**请求示例**:
```bash
curl -X POST "http://your-domain.com/api/admin_push/pushWarning" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "磁盘空间不足，建议清理无用文件。"
  }'
```

### 4. 推送错误消息 - `/api/admin_push/pushError`

**请求方式**: POST

**参数说明**:
- `title` (string, 可选): 通知标题，默认为"错误警报"
- `content` (string, 必填): 通知内容

**请求示例**:
```bash
curl -X POST "http://your-domain.com/api/admin_push/pushError" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "检测到系统异常，请立即处理！"
  }'
```

### 5. 推送成功消息 - `/api/admin_push/pushSuccess`

**请求方式**: POST

**参数说明**:
- `title` (string, 可选): 通知标题，默认为"操作成功"
- `content` (string, 必填): 通知内容

**请求示例**:
```bash
curl -X POST "http://your-domain.com/api/admin_push/pushSuccess" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "数据备份已完成，系统运行稳定。"
  }'
```

### 6. 推送信息消息 - `/api/admin_push/pushInfo`

**请求方式**: POST

**参数说明**:
- `title` (string, 可选): 通知标题，默认为"信息提示"
- `content` (string, 必填): 通知内容

**请求示例**:
```bash
curl -X POST "http://your-domain.com/api/admin_push/pushInfo" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "今日访问量已达到新高，系统表现良好。"
  }'
```

### 7. 批量推送消息 - `/api/admin_push/batchPush`

**请求方式**: POST

**参数说明**:
- `messages` (array, 必填): 消息列表，每个消息包含title, content, type等字段

**请求示例**:
```bash
curl -X POST "http://your-domain.com/api/admin_push/batchPush" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "title": "系统通知",
        "content": "系统维护完成",
        "type": "system"
      },
      {
        "title": "数据备份",
        "content": "数据备份成功",
        "type": "success"
      }
    ]
  }'
```

### 8. 测试连接状态 - `/api/admin_push/testConnection`

**请求方式**: GET

**请求示例**:
```bash
curl -X GET "http://your-domain.com/api/admin_push/testConnection"
```

### 9. 获取在线管理员 - `/api/admin_push/getOnlineAdmins`

**请求方式**: GET

**请求示例**:
```bash
curl -X GET "http://your-domain.com/api/admin_push/getOnlineAdmins"
```

## 推送方式说明

### 1. WebSocket直推 (websocket)
- 直接通过WebSocket连接推送消息
- 实时性最高，延迟最低
- 需要管理员在线且WebSocket连接正常

### 2. Redis发布订阅 (redis)
- 通过Redis的发布订阅机制推送
- 支持分布式部署
- 需要Swoole服务器订阅Redis频道

### 3. 双重推送 (both) - 推荐
- 同时使用WebSocket和Redis两种方式
- 确保消息送达率最高
- 推荐在生产环境使用

## 消息类型说明

| 类型 | 说明 | 图标 | 样式 |
|------|------|------|------|
| system | 系统通知 | 🔔 | 蓝色渐变 |
| success | 成功消息 | ✅ | 绿色渐变 |
| error | 错误消息 | ❌ | 红色渐变 |
| warning | 警告消息 | ⚠️ | 橙色渐变 |
| info | 信息消息 | 💡 | 青色渐变 |

## 测试页面

访问 `http://your-domain.com/admin_push_test.html` 可以使用可视化界面测试所有推送功能。

## 注意事项

1. **频道名称**: 确保Swoole配置中的subscribe频道名为 `admin_notification`
2. **管理员连接**: 管理员需要在后台页面建立WebSocket连接才能接收消息
3. **Redis配置**: 确保Redis服务正常运行且配置正确
4. **权限设置**: 所有接口都设置为无需登录验证，生产环境请根据需要调整
5. **日志记录**: 所有推送操作都会记录详细日志，便于调试

## 响应格式

所有接口都返回统一的JSON格式：

```json
{
  "code": 1,
  "msg": "推送成功",
  "data": {
    "websocket": {
      "success": true,
      "method": "websocket_direct",
      "message": "直接WebSocket推送成功"
    },
    "redis": {
      "success": true,
      "method": "redis_publish",
      "subscribers": 2,
      "message": "Redis推送成功，2个订阅者收到"
    }
  }
}
```

- `code`: 1表示成功，0表示失败
- `msg`: 响应消息
- `data`: 详细的响应数据

## 故障排查

1. **推送失败**: 检查Swoole服务器和Redis是否正常运行
2. **无法接收**: 确认管理员已建立WebSocket连接
3. **频道不匹配**: 检查配置文件中的subscribe频道名称
4. **权限问题**: 确认API接口权限配置正确