<?php
/**
 * 简单的Redis订阅测试
 */

$apiBase = 'http://your-domain.com/api/admin_push';

function testAPI($endpoint, $data = null) {
    global $apiBase;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiBase . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

echo "🔧 简单Redis订阅测试\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 50) . "\n";

// 1. 重建管理员连接
echo "1. 重建管理员连接...\n";
$result = testAPI('/rebuildAdminConnection', [
    'admin_id' => 1,
    'nickname' => 'admin',
    'fd' => 1
]);

if ($result['data']['code'] == 1) {
    echo "✅ 管理员连接重建成功\n";
} else {
    echo "❌ 管理员连接重建失败\n";
    exit(1);
}

sleep(2);

// 2. 检查Redis订阅状态
echo "\n2. 检查Redis订阅状态...\n";
$result = testAPI('/checkRedisSubscribe');

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    $publishTest = $data['publish_test'];
    $subscribers = $publishTest['subscribers'];
    
    echo "Redis连接: " . ($data['redis_connection'] ? '✅ 正常' : '❌ 失败') . "\n";
    echo "发布测试订阅者: {$subscribers}\n";
    
    if ($subscribers > 0) {
        echo "✅ Redis订阅功能正常！\n";
    } else {
        echo "❌ Redis订阅功能异常，没有订阅者\n";
        echo "这可能是正常的，因为Redis订阅在第一次WebSocket连接时才启动\n";
    }
} else {
    echo "❌ Redis订阅状态检查失败\n";
}

sleep(2);

// 3. 测试推送（这会触发Redis订阅启动）
echo "\n3. 测试推送消息...\n";
$result = testAPI('/pushMessage', [
    'title' => '测试推送',
    'content' => '这是一条测试推送消息 - ' . date('H:i:s'),
    'type' => 'system',
    'method' => 'both'
]);

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    
    if (isset($data['websocket'])) {
        $wsResult = $data['websocket'];
        echo "WebSocket推送: " . ($wsResult['success'] ? '✅ 成功' : '❌ 失败') . "\n";
    }
    
    if (isset($data['redis'])) {
        $redisResult = $data['redis'];
        echo "Redis推送: " . ($redisResult['success'] ? '✅ 成功' : '❌ 失败') . "\n";
        if (isset($redisResult['subscribers'])) {
            echo "- 订阅者: " . $redisResult['subscribers'] . "\n";
        }
    }
} else {
    echo "❌ 推送测试失败: " . ($result['data']['msg'] ?? 'unknown') . "\n";
}

sleep(2);

// 4. 再次检查Redis订阅状态
echo "\n4. 再次检查Redis订阅状态...\n";
$result = testAPI('/checkRedisSubscribe');

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    $publishTest = $data['publish_test'];
    $subscribers = $publishTest['subscribers'];
    
    echo "发布测试订阅者: {$subscribers}\n";
    
    if ($subscribers > 0) {
        echo "✅ Redis订阅功能现在正常工作！\n";
        echo "请检查后台管理员页面是否收到推送消息\n";
    } else {
        echo "❌ Redis订阅功能仍然异常\n";
    }
} else {
    echo "❌ Redis订阅状态检查失败\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "📋 测试总结:\n";
echo "1. 如果最后的订阅者数量大于0，说明功能正常\n";
echo "2. 请检查后台管理员页面是否收到推送消息\n";
echo "3. 如果仍有问题，请检查Swoole服务器日志\n";
echo "\n✅ 简单测试完成！\n";
?>