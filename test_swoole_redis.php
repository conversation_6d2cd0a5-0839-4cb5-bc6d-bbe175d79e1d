<?php
/**
 * 测试Swoole协程Redis连接
 */

use Swoole\Coroutine;

// 启动协程
Coroutine\run(function () {
    echo "🔍 测试Swoole协程Redis连接\n";
    echo "时间: " . date('Y-m-d H:i:s') . "\n";
    echo str_repeat("=", 50) . "\n";

    try {
        // 创建Swoole协程Redis客户端
        $redis = new \Swoole\Coroutine\Redis();
        
        $redisConfig = [
            'host' => 'r-bp180wgq6voudt9d75.redis.rds.aliyuncs.com',
            'port' => 6379,
            'timeout' => 10,
            'database' => 0
        ];
        
        echo "1. 测试连接参数:\n";
        echo "   主机: {$redisConfig['host']}\n";
        echo "   端口: {$redisConfig['port']}\n";
        echo "   超时: {$redisConfig['timeout']}秒\n";
        echo "   数据库: {$redisConfig['database']}\n\n";
        
        // 设置连接选项
        echo "2. 设置连接选项...\n";
        $redis->setOptions([
            'timeout' => $redisConfig['timeout'],
            'read_timeout' => -1,
            'connect_timeout' => $redisConfig['timeout']
        ]);
        echo "✅ 连接选项设置完成\n\n";
        
        // 连接Redis
        echo "3. 尝试连接Redis...\n";
        $startTime = microtime(true);
        $connectResult = $redis->connect($redisConfig['host'], $redisConfig['port']);
        $connectTime = round((microtime(true) - $startTime) * 1000, 2);
        
        echo "连接结果: " . var_export($connectResult, true) . "\n";
        echo "连接耗时: {$connectTime}ms\n";
        
        if (!$connectResult) {
            $errMsg = $redis->errMsg ?: '未知错误';
            echo "❌ 连接失败: {$errMsg}\n";
            return;
        }
        
        echo "✅ 连接成功\n\n";
        
        // 选择数据库
        echo "4. 选择数据库...\n";
        $selectResult = $redis->select($redisConfig['database']);
        echo "选择结果: " . var_export($selectResult, true) . "\n";
        
        if (!$selectResult) {
            $errMsg = $redis->errMsg ?: '未知错误';
            echo "❌ 数据库选择失败: {$errMsg}\n";
            return;
        }
        
        echo "✅ 数据库选择成功\n\n";
        
        // 测试ping
        echo "5. 测试ping...\n";
        $pingResult = $redis->ping();
        echo "Ping结果: " . var_export($pingResult, true) . "\n";
        echo "Ping结果类型: " . gettype($pingResult) . "\n";
        
        if ($pingResult === false || $pingResult === null) {
            echo "❌ Ping测试失败\n";
            return;
        }
        
        echo "✅ Ping测试成功\n\n";
        
        // 测试基本操作
        echo "6. 测试基本操作...\n";
        $setResult = $redis->set('swoole_test', 'hello_world');
        echo "SET结果: " . var_export($setResult, true) . "\n";
        
        $getResult = $redis->get('swoole_test');
        echo "GET结果: " . var_export($getResult, true) . "\n";
        
        $delResult = $redis->del('swoole_test');
        echo "DEL结果: " . var_export($delResult, true) . "\n";
        
        echo "✅ 基本操作测试成功\n\n";
        
        // 测试订阅功能
        echo "7. 测试订阅功能...\n";
        
        // 创建发布者
        $publisher = new \Swoole\Coroutine\Redis();
        if ($publisher->connect($redisConfig['host'], $redisConfig['port'])) {
            $publisher->select($redisConfig['database']);
            
            // 在另一个协程中发布消息
            go(function () use ($publisher) {
                Coroutine::sleep(1); // 等待1秒
                $testMessage = json_encode([
                    'event' => 'test',
                    'data' => ['message' => 'Swoole协程Redis测试消息']
                ]);
                $publishResult = $publisher->publish('test_channel', $testMessage);
                echo "发布消息结果: " . var_export($publishResult, true) . "\n";
                $publisher->close();
            });
            
            // 订阅消息
            $subscribeResult = $redis->subscribe(['test_channel']);
            echo "订阅结果: " . var_export($subscribeResult, true) . "\n";
            
            if ($subscribeResult) {
                echo "✅ 订阅成功，等待消息...\n";
                
                // 接收消息（最多等待5秒）
                $timeout = time() + 5;
                while (time() < $timeout) {
                    $message = $redis->recv();
                    if ($message !== false) {
                        echo "收到消息: " . var_export($message, true) . "\n";
                        if (is_array($message) && $message[0] === 'message') {
                            echo "✅ 订阅功能测试成功\n";
                            break;
                        }
                    }
                    Coroutine::sleep(0.1);
                }
            } else {
                echo "❌ 订阅失败\n";
            }
        }
        
        // 关闭连接
        $redis->close();
        echo "\n✅ 所有测试完成\n";
        
    } catch (\Exception $e) {
        echo "❌ 测试异常: " . $e->getMessage() . "\n";
        echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
    }
});
?>