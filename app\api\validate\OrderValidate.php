<?php

namespace app\api\validate;

use app\common\basics\Validate;

class OrderValidate extends Validate
{
    protected $rule = [
        'id' => 'require',
        'cart_id' => 'require',
        'goods' => 'require',
        'address_id' => 'require|checkParam',
        'consignee' => 'require',
        'province' => 'require',
        'city' => 'require',
        'district' => 'require',
        'address' => 'require',
        'mobile' => 'require|mobile',
        'after_sale_id' => 'require|integer',
    ];

    protected $message = [
        'id' => '参数错误',
        'cart_id' => '参数类型错误',
        'goods' => '请选择商品',
        'address_id' => '请选择收货地址',
        'consignee.require' => '请填写收件人',
        'province.require' => '地址参数缺失',
        'city.require' => '地址参数缺失',
        'district.require' => '地址参数缺失',
        'address.require' => '请填写详细地址',
        'mobile.require' => '请填写手机号码',
        'mobile.mobile' => '请填写正确的手机号码',
        'after_sale_id.require' => '售后申请ID不能为空',
        'after_sale_id.integer' => '售后申请ID格式错误',
    ];


    protected $scene = [
        'add' => ['address_id'],
        'detail' => ['id'],
        'editAddress' => ['id','consignee','province','city','district','address','mobile'],
        'cancelAfterSale' => ['after_sale_id'],
    ];


    /**
     * @notes 参数验证
     * @param $value
     * @param $arr
     * @param $data
     * @return bool|string
     * <AUTHOR>
     * @date 2021/7/13 6:29 下午
     */
    public function checkParam($value, $arr, $data)
    {
        if (!isset($data['goods']) && !isset($data['cart_id'])) {
            return '参数有误';
        }
        return true;
    }

}
