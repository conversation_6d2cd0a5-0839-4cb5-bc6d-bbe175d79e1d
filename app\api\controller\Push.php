<?php

namespace app\api\controller;

use app\admin\logic\WechatMerchantTransferLogic;
use app\common\basics\Api;
use app\common\model\WithdrawApply;
use app\api\controller\Notification; // 引入Notification控制器
use app\api\controller\Websocket; // 引入Websocket控制器
use think\facade\Request; // 引入Request类
use think\facade\Log; // 引入日志类
use app\common\server\ConfigServer;
use Swoole\Server as SwooleServer;

Class Push extends Api
{
    public $like_not_need_login = ['rpcServer'];
    /**
     * RPC服务入口方法
     */
    public function rpcServer(Request $request)
    {
        // 创建一个新的Swoole服务器对象
        $server = new SwooleServer(config('rpc.server.host'), config('rpc.server.port'));
        
        // 设置异步任务的工作进程数量
        $server->set(array('task_worker_num' => config('rpc.server.task_worker_num')));
        
        // 监听连接事件
        $server->on('connect', function ($server, $fd) {
            echo "客户端 " . $fd . " 已连接
";
        });
        
        // 监听数据接收事件
        $server->on('receive', function ($server, $fd, $from_id, $data) {
            // 处理接收到的数据
            $message = json_decode($data, true);
            
            // TODO: 消息推送逻辑
            
            // 发送响应数据
            $server->send($fd, '消息已成功接收');
        });
        
        // 监听关闭事件
        $server->on('close', function ($server, $fd) {
            echo "客户端 " . $fd . " 已断开连接
";
        });
        
        // 启动RPC服务
        $server->start();
    }

}