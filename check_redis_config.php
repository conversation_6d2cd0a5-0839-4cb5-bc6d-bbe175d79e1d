<?php
/**
 * Redis配置检查脚本
 */

// 引入ThinkPHP框架
require_once 'vendor/autoload.php';

// 启动应用
$app = new think\App();
$app->initialize();

echo "🔧 Redis配置检查\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";

// 1. 检查Swoole配置中的Redis设置
echo "1. Swoole配置中的Redis设置:\n";
try {
    $swooleConfig = config('swoole.websocket.room.redis');
    if (is_array($swooleConfig)) {
        echo "✅ 找到Redis配置:\n";
        foreach ($swooleConfig as $key => $value) {
            echo "  - {$key}: {$value}\n";
        }
    } else {
        echo "❌ 未找到Redis配置\n";
    }
} catch (Exception $e) {
    echo "❌ 读取配置失败: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 60) . "\n";

// 2. 测试配置中的Redis连接
echo "2. 测试配置中的Redis连接:\n";
try {
    $redisConfig = config('swoole.websocket.room.redis');
    if (is_array($redisConfig)) {
        $redis = new Redis();
        
        $host = $redisConfig['host'] ?? 'localhost';
        $port = $redisConfig['port'] ?? 6379;
        $database = $redisConfig['database'] ?? 0;
        $timeout = $redisConfig['timeout'] ?? 5;
        
        echo "连接参数: {$host}:{$port}, DB:{$database}, 超时:{$timeout}s\n";
        
        $startTime = microtime(true);
        if ($redis->connect($host, $port, $timeout)) {
            $connectTime = round((microtime(true) - $startTime) * 1000, 2);
            echo "✅ 连接成功 (耗时: {$connectTime}ms)\n";
            
            if ($redis->select($database)) {
                echo "✅ 数据库选择成功\n";
            }
            
            $ping = $redis->ping();
            echo "Ping测试: " . ($ping ? '✅ PONG' : '❌ 无响应') . "\n";
            
            // 测试发布
            $testData = json_encode([
                'event' => 'admin_notification',
                'data' => [
                    'type' => 'system',
                    'title' => '配置测试',
                    'content' => '这是配置测试消息',
                    'timestamp' => time()
                ]
            ]);
            
            $publishResult = $redis->publish('admin_notification', $testData);
            echo "发布测试: " . ($publishResult >= 0 ? "✅ 成功，{$publishResult}个订阅者" : "❌ 失败") . "\n";
            
            $redis->close();
        } else {
            echo "❌ 连接失败\n";
        }
    } else {
        echo "❌ Redis配置不是数组格式\n";
    }
} catch (Exception $e) {
    echo "❌ 测试异常: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 60) . "\n";

// 3. 检查当前使用的硬编码配置
echo "3. 当前硬编码配置测试:\n";
$hardcodedConfig = [
    'host' => 'r-bp180wgq6voudt9d75.redis.rds.aliyuncs.com',
    'port' => 6379,
    'database' => 0
];

try {
    $redis = new Redis();
    echo "连接参数: {$hardcodedConfig['host']}:{$hardcodedConfig['port']}\n";
    
    $startTime = microtime(true);
    if ($redis->connect($hardcodedConfig['host'], $hardcodedConfig['port'], 5)) {
        $connectTime = round((microtime(true) - $startTime) * 1000, 2);
        echo "✅ 硬编码配置连接成功 (耗时: {$connectTime}ms)\n";
        
        $redis->select($hardcodedConfig['database']);
        
        $info = $redis->info();
        if (is_array($info)) {
            echo "Redis信息:\n";
            echo "  - 版本: " . ($info['redis_version'] ?? '未知') . "\n";
            echo "  - 连接数: " . ($info['connected_clients'] ?? '未知') . "\n";
            echo "  - 内存使用: " . ($info['used_memory_human'] ?? '未知') . "\n";
            echo "  - 运行时间: " . ($info['uptime_in_seconds'] ?? '未知') . "秒\n";
        }
        
        $redis->close();
    } else {
        echo "❌ 硬编码配置连接失败\n";
    }
} catch (Exception $e) {
    echo "❌ 硬编码配置测试异常: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";

// 4. 建议
echo "📋 配置建议:\n";
echo "1. 确保Swoole配置文件中的Redis设置正确\n";
echo "2. 如果使用阿里云ECS，建议使用内网地址连接Redis\n";
echo "3. 设置合适的连接超时时间，避免长时间等待\n";
echo "4. 监控Redis连接数，确保不超过实例限制\n";
echo "5. 考虑使用Redis连接池来管理连接\n";

echo "\n✅ 配置检查完成\n";
?>