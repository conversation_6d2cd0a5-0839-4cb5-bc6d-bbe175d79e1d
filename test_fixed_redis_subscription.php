<?php
/**
 * 测试修复后的Redis订阅功能
 */

$apiBase = 'http://your-domain.com/api/admin_push';

function testAPI($endpoint, $data = null) {
    global $apiBase;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiBase . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

echo "🔧 Redis订阅功能测试（修复后）\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";

echo "⚠️  请确保已重启Swoole服务器！\n";
echo "重启命令: php think swoole:server restart\n";
echo str_repeat("-", 60) . "\n";

// 等待用户确认
echo "请确认Swoole服务器已重启，然后按回车键继续...";
fgets(STDIN);

// 1. 重建管理员连接
echo "1. 重建管理员连接...\n";
$result = testAPI('/rebuildAdminConnection', [
    'admin_id' => 1,
    'nickname' => 'admin',
    'fd' => 1
]);

if ($result['data']['code'] == 1) {
    echo "✅ 管理员连接重建成功\n";
} else {
    echo "❌ 管理员连接重建失败\n";
}

sleep(2);

// 2. 检查Redis订阅状态
echo "\n2. 检查Redis订阅状态...\n";
$result = testAPI('/checkRedisSubscribe');

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    $publishTest = $data['publish_test'];
    $subscribers = $publishTest['subscribers'];
    
    echo "Redis连接: " . ($data['redis_connection'] ? '✅ 正常' : '❌ 失败') . "\n";
    echo "发布测试订阅者: {$subscribers}\n";
    
    if ($subscribers > 0) {
        echo "✅ Redis订阅功能正常！\n";
    } else {
        echo "❌ Redis订阅功能异常，没有订阅者\n";
        echo "请检查Swoole服务器日志，确认Redis订阅是否正常启动\n";
    }
} else {
    echo "❌ Redis订阅状态检查失败\n";
}

sleep(2);

// 3. 测试Redis订阅推送
echo "\n3. 测试Redis订阅推送...\n";
for ($i = 1; $i <= 3; $i++) {
    echo "发送第 {$i} 条测试消息...\n";
    
    $result = testAPI('/testDirectPush', [
        'title' => "Redis订阅测试 #{$i}",
        'content' => "这是第{$i}条Redis订阅功能测试消息 - " . date('H:i:s')
    ]);

    if ($result['data']['code'] == 1) {
        $data = $result['data']['data'];
        $publishResult = $data['publish_result'] ?? 0;
        
        if ($publishResult > 0) {
            echo "✅ 第{$i}条消息推送成功！订阅者: {$publishResult}\n";
        } else {
            echo "❌ 第{$i}条消息推送失败，没有订阅者\n";
        }
    } else {
        echo "❌ 第{$i}条消息推送测试失败\n";
    }
    
    sleep(1);
}

sleep(2);

// 4. 测试不同类型的推送
echo "\n4. 测试不同类型的推送...\n";
$types = [
    ['type' => 'success', 'title' => '成功通知', 'content' => '操作已成功完成！'],
    ['type' => 'warning', 'title' => '警告提醒', 'content' => '请注意系统警告信息！'],
    ['type' => 'error', 'title' => '错误警报', 'content' => '系统发生错误，请立即处理！'],
    ['type' => 'info', 'title' => '信息提示', 'content' => '这是一条信息提示。']
];

foreach ($types as $typeInfo) {
    echo "推送{$typeInfo['title']}...\n";
    
    $result = testAPI('/pushMessage', [
        'title' => $typeInfo['title'],
        'content' => $typeInfo['content'] . ' - ' . date('H:i:s'),
        'type' => $typeInfo['type'],
        'method' => 'redis'
    ]);

    if ($result['data']['code'] == 1) {
        $data = $result['data']['data'];
        if (isset($data['redis']) && $data['redis']['success']) {
            echo "✅ {$typeInfo['title']}推送成功\n";
        } else {
            echo "❌ {$typeInfo['title']}推送失败\n";
        }
    } else {
        echo "❌ {$typeInfo['title']}推送测试失败\n";
    }
    
    sleep(1);
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "📋 测试总结:\n";
echo "1. 如果所有推送都成功，说明Redis订阅功能正常工作\n";
echo "2. 请检查后台管理员页面是否收到所有推送消息\n";
echo "3. 如果仍有问题，请检查Swoole服务器日志\n";
echo "4. 确认管理员已登录后台并建立WebSocket连接\n";
echo "\n✅ Redis订阅功能测试完成！\n";

echo "\n📝 如果测试成功，可以使用以下API进行推送:\n";
echo "- 基础推送: POST /api/admin_push/pushMessage\n";
echo "- 系统通知: POST /api/admin_push/pushSystemNotice\n";
echo "- 成功消息: POST /api/admin_push/pushSuccess\n";
echo "- 警告消息: POST /api/admin_push/pushWarning\n";
echo "- 错误消息: POST /api/admin_push/pushError\n";
echo "- 信息消息: POST /api/admin_push/pushInfo\n";
?>