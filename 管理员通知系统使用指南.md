# 管理员通知系统使用指南

## 概述

管理员通知系统已经优化完成，现在可以通过简单的方法调用来推送通知给在线的后台管理员。系统基于Redis发布订阅机制，支持实时推送。

## 主要功能

1. **实时通知推送** - 向所有在线管理员推送通知
2. **定向推送** - 向特定管理员推送通知
3. **批量推送** - 批量推送多条通知
4. **多种通知类型** - 支持系统、成功、错误、警告、信息等类型

## 使用方法

### 1. 基础推送方法

```php
use app\common\service\AdminNotificationService;

// 基础推送
AdminNotificationService::push('通知标题', '通知内容', 'system', '', 0);

// 快捷方法
AdminNotificationService::system('系统通知', '这是一条系统通知');
AdminNotificationService::success('操作成功', '数据保存成功');
AdminNotificationService::error('操作失败', '数据保存失败');
AdminNotificationService::warning('警告提醒', '磁盘空间不足');
AdminNotificationService::info('信息提示', '有新的订单');
```

### 2. 定向推送

```php
// 推送给特定管理员
AdminNotificationService::pushToAdmin(1, '个人通知', '您有新的消息', 'info');
```

### 3. 批量推送

```php
$notifications = [
    ['title' => '通知1', 'content' => '内容1', 'type' => 'system'],
    ['title' => '通知2', 'content' => '内容2', 'type' => 'success'],
    ['title' => '通知3', 'content' => '内容3', 'type' => 'error']
];

$result = AdminNotificationService::batchPush($notifications);
```

### 4. API接口调用

```bash
# 推送通知
curl -X POST "http://your-domain/api/admin_push/pushMessage" \
  -d "title=测试通知&content=这是一条测试通知&type=system"

# 推送系统通知
curl -X POST "http://your-domain/api/admin_push/pushSystemNotice" \
  -d "content=系统维护通知"

# 推送成功通知
curl -X POST "http://your-domain/api/admin_push/pushSuccess" \
  -d "content=操作执行成功"

# 推送错误通知
curl -X POST "http://your-domain/api/admin_push/pushError" \
  -d "content=系统出现错误"

# 推送警告通知
curl -X POST "http://your-domain/api/admin_push/pushWarning" \
  -d "content=磁盘空间不足"

# 推送信息通知
curl -X POST "http://your-domain/api/admin_push/pushInfo" \
  -d "content=有新的用户注册"
```

## 参数说明

### push() 方法参数

- `title` (string) - 通知标题
- `content` (string) - 通知内容
- `type` (string) - 通知类型：system, success, error, warning, info
- `url` (string) - 点击跳转URL（可选）
- `icon` (int) - 图标类型：0-默认，1-成功，2-错误，3-警告，4-信息

### 通知类型说明

- `system` - 系统通知（默认图标）
- `success` - 成功通知（绿色图标）
- `error` - 错误通知（红色图标）
- `warning` - 警告通知（黄色图标）
- `info` - 信息通知（蓝色图标）

## 实际应用场景

### 1. 订单处理通知

```php
// 新订单通知
AdminNotificationService::info('新订单', "订单号：{$orderNo}，金额：{$amount}元");

// 订单支付成功
AdminNotificationService::success('支付成功', "订单 {$orderNo} 支付成功");

// 订单异常
AdminNotificationService::error('订单异常', "订单 {$orderNo} 支付失败");
```

### 2. 系统监控通知

```php
// 系统维护
AdminNotificationService::system('系统维护', '系统将于今晚22:00进行维护');

// 服务器警告
AdminNotificationService::warning('服务器警告', 'CPU使用率超过80%');

// 错误报告
AdminNotificationService::error('系统错误', '数据库连接失败');
```

### 3. 用户活动通知

```php
// 新用户注册
AdminNotificationService::info('新用户', "用户 {$username} 注册成功");

// 用户投诉
AdminNotificationService::warning('用户投诉', "收到用户投诉，订单号：{$orderNo}");
```

## 工具方法

```php
// 检查在线管理员数量
$count = AdminNotificationService::getOnlineAdminCount();

// 检查Redis连接状态
$isConnected = AdminNotificationService::checkConnection();
```

## 注意事项

1. **Redis连接** - 确保Redis服务正常运行
2. **WebSocket服务** - 确保Swoole WebSocket服务正常启动
3. **管理员在线** - 只有在线的管理员才能收到通知
4. **频率控制** - 批量推送时会自动添加延迟，避免推送过快

## 系统优化

1. **移除冗余日志** - 减少不必要的日志输出
2. **连接重试** - 自动重连机制，提高稳定性
3. **错误处理** - 完善的异常处理机制
4. **性能优化** - 使用协程处理，提高并发性能

## 故障排查

如果通知推送失败，请检查：

1. Redis服务是否正常
2. Swoole WebSocket服务是否启动
3. 管理员是否在线
4. 网络连接是否正常

可以通过以下API检查系统状态：

```bash
# 检查连接状态
curl "http://your-domain/api/admin_push/testConnection"

# 获取在线管理员
curl "http://your-domain/api/admin_push/getOnlineAdmins"
```