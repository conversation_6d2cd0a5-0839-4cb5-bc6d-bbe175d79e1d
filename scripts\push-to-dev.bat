@echo off
chcp 65001 >nul
echo 开始推送到测试环境...

:: 检查是否有未提交的更改
git status --porcelain > temp_status.txt
set /p changes=<temp_status.txt
del temp_status.txt

if not "%changes%"=="" (
    echo 发现未提交的更改，正在提交...
    git add .
    set /p commit_msg="请输入提交信息 (直接回车使用默认信息): "
    if "%commit_msg%"=="" set commit_msg=自动提交更改
    git commit -m "%commit_msg%"
)

:: 推送到develop分支
echo 推送到develop分支...
git push origin develop

if %errorlevel% equ 0 (
    echo 推送成功！
    echo 测试环境将自动部署，请稍等片刻后访问: https://dev.huohanghang.cn
) else (
    echo 推送失败，请检查网络连接和权限
)

pause