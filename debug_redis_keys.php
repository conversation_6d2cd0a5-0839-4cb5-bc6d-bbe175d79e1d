<?php
/**
 * Redis键值调试脚本
 * 直接连接Redis查看实际的键值情况
 */

// 引入ThinkPHP框架
require_once 'vendor/autoload.php';

// 启动应用
$app = new think\App();
$app->initialize();

use think\facade\Cache;
use think\facade\Log;

echo "🔍 Redis键值调试\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";

try {
    $redis = Cache::store('redis');
    $prefix = config('default.websocket_prefix', 'socket_');
    $adminPrefix = $prefix . 'admin_';
    
    echo "配置信息:\n";
    echo "- websocket_prefix: {$prefix}\n";
    echo "- admin_prefix: {$adminPrefix}\n";
    echo "\n";
    
    // 获取所有相关的键
    echo "🔑 所有Socket相关键:\n";
    $allSocketKeys = $redis->keys($prefix . '*');
    if (empty($allSocketKeys)) {
        echo "❌ 没有找到任何socket相关的键\n";
    } else {
        foreach ($allSocketKeys as $key) {
            $value = $redis->get($key);
            $type = $redis->type($key);
            echo "- {$key} ({$type}): ";
            
            if ($type === 'string') {
                echo $value . "\n";
            } elseif ($type === 'set') {
                $members = $redis->sMembers($key);
                echo json_encode($members) . "\n";
            } else {
                echo "类型: {$type}\n";
            }
        }
    }
    
    echo "\n🔑 管理员相关键:\n";
    $adminKeys = $redis->keys($adminPrefix . '*');
    if (empty($adminKeys)) {
        echo "❌ 没有找到任何管理员相关的键\n";
    } else {
        foreach ($adminKeys as $key) {
            $value = $redis->get($key);
            $type = $redis->type($key);
            echo "- {$key} ({$type}): ";
            
            if ($type === 'string') {
                echo $value . "\n";
                // 如果是JSON，尝试解析
                $decoded = json_decode($value, true);
                if ($decoded) {
                    echo "  解析后: " . json_encode($decoded, JSON_UNESCAPED_UNICODE) . "\n";
                }
            } elseif ($type === 'set') {
                $members = $redis->sMembers($key);
                echo json_encode($members) . "\n";
            } else {
                echo "类型: {$type}\n";
            }
        }
    }
    
    echo "\n🔑 特定模式搜索:\n";
    $patterns = [
        $prefix . 'fd_*',
        $adminPrefix . 'fd_*',
        $adminPrefix . 'admin_*',
        $prefix . 'admin*',
        '*admin*',
        '*socket*'
    ];
    
    foreach ($patterns as $pattern) {
        echo "模式: {$pattern}\n";
        $keys = $redis->keys($pattern);
        if (empty($keys)) {
            echo "  ❌ 没有匹配的键\n";
        } else {
            foreach ($keys as $key) {
                $value = $redis->get($key);
                echo "  - {$key}: {$value}\n";
            }
        }
        echo "\n";
    }
    
    // 尝试通过CombinedHandler获取Room信息
    echo "🏠 Room信息:\n";
    try {
        $handler = app(\app\common\websocket\CombinedHandler::class);
        $adminGroupClients = $handler->room->getClients('admin_group');
        echo "admin_group房间成员: " . json_encode($adminGroupClients) . "\n";
    } catch (\Exception $e) {
        echo "❌ 获取Room信息失败: " . $e->getMessage() . "\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "✅ 调试完成\n";
?>