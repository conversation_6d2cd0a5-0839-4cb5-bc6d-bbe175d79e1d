<?php
/**
 * 检查配置值
 */

// 引入ThinkPHP框架
require_once 'vendor/autoload.php';

// 启动应用
$app = new think\App();
$app->initialize();

echo "🔧 配置检查\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";

// 检查websocket_prefix配置
$websocketPrefix = config('default.websocket_prefix');
echo "websocket_prefix: " . var_export($websocketPrefix, true) . "\n";

$adminPrefix = $websocketPrefix . 'admin_';
echo "admin_prefix: " . var_export($adminPrefix, true) . "\n";

// 检查其他相关配置
$allDefaultConfig = config('default');
echo "\n所有default配置:\n";
foreach ($allDefaultConfig as $key => $value) {
    if (strpos($key, 'websocket') !== false || strpos($key, 'socket') !== false) {
        echo "- {$key}: " . var_export($value, true) . "\n";
    }
}

// 检查swoole配置
echo "\nSwoole WebSocket配置:\n";
$swooleConfig = config('swoole.websocket');
if ($swooleConfig) {
    echo "- handler: " . ($swooleConfig['handler'] ?? 'null') . "\n";
    echo "- room type: " . ($swooleConfig['room']['type'] ?? 'null') . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "✅ 配置检查完成\n";
?>