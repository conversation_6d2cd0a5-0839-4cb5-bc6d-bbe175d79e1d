<?php
/**
 * 测试Redis订阅功能
 */

$apiBase = 'http://your-domain.com/api/admin_push';

function testAPI($endpoint, $data = null) {
    global $apiBase;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiBase . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

echo "🔧 Redis订阅功能测试（修改后）\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";

echo "⚠️  注意：请确保已重启Swoole服务器以加载新的Redis订阅功能！\n";
echo str_repeat("-", 60) . "\n";

// 1. 重建管理员连接
echo "1. 重建管理员连接...\n";
$result = testAPI('/rebuildAdminConnection', [
    'admin_id' => 1,
    'nickname' => 'admin',
    'fd' => 1
]);

if ($result['data']['code'] == 1) {
    echo "✅ 管理员连接重建成功\n";
} else {
    echo "❌ 管理员连接重建失败\n";
}

sleep(1);

// 2. 检查Redis订阅状态
echo "\n2. 检查Redis订阅状态...\n";
$result = testAPI('/checkRedisSubscribe');

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    $publishTest = $data['publish_test'];
    $subscribers = $publishTest['subscribers'];
    
    echo "Redis连接: " . ($data['redis_connection'] ? '✅ 正常' : '❌ 失败') . "\n";
    echo "发布测试订阅者: {$subscribers}\n";
    
    if ($subscribers > 0) {
        echo "✅ Redis订阅功能正常！\n";
    } else {
        echo "❌ Redis订阅功能异常，没有订阅者\n";
        echo "请检查Swoole服务器是否已重启并加载新的订阅功能\n";
    }
} else {
    echo "❌ Redis订阅状态检查失败\n";
}

sleep(2);

// 3. 测试Redis订阅推送
echo "\n3. 测试Redis订阅推送...\n";
$result = testAPI('/testDirectPush', [
    'title' => 'Redis订阅测试（修改后）',
    'content' => '这是修改Redis订阅功能后的测试消息 - ' . date('H:i:s')
]);

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    $publishResult = $data['publish_result'] ?? 0;
    
    if ($publishResult > 0) {
        echo "✅ Redis订阅推送成功！订阅者: {$publishResult}\n";
        echo "请检查后台管理员页面是否收到消息\n";
    } else {
        echo "❌ Redis订阅推送失败，没有订阅者\n";
    }
} else {
    echo "❌ Redis订阅推送测试失败: " . ($result['data']['msg'] ?? 'unknown') . "\n";
}

sleep(2);

// 4. 测试常规推送
echo "\n4. 测试常规推送...\n";
$result = testAPI('/pushMessage', [
    'title' => '常规推送测试（修改后）',
    'content' => '这是修改后的常规推送测试消息 - ' . date('H:i:s'),
    'type' => 'success',
    'method' => 'both'
]);

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    
    if (isset($data['redis'])) {
        $redisResult = $data['redis'];
        echo "Redis推送: " . ($redisResult['success'] ? '✅ 成功' : '❌ 失败') . "\n";
        if (isset($redisResult['subscribers'])) {
            echo "- 订阅者: " . $redisResult['subscribers'] . "\n";
        }
    }
    
    if (isset($data['websocket'])) {
        $wsResult = $data['websocket'];
        echo "WebSocket推送: " . ($wsResult['success'] ? '✅ 成功' : '❌ 失败') . "\n";
    }
} else {
    echo "❌ 常规推送测试失败\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "📋 测试总结:\n";
echo "1. 如果订阅者数量大于0，说明Redis订阅功能正常\n";
echo "2. 如果仍然为0，请检查Swoole服务器日志\n";
echo "3. 确保Swoole服务器已重启并加载新的订阅功能\n";
echo "4. 检查后台管理员页面是否收到推送消息\n";
echo "\n✅ Redis订阅功能测试完成！\n";

echo "\n🚀 如果测试失败，请重启Swoole服务器:\n";
echo "php think swoole:server restart\n";
?>