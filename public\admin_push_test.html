<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理员推送测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        .btn-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .quick-messages {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        .quick-msg {
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .quick-msg:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }
        .quick-msg h4 {
            margin: 0 0 5px 0;
            font-size: 14px;
            color: #495057;
        }
        .quick-msg p {
            margin: 0;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 后台管理员推送测试</h1>
        
        <!-- 快速消息模板 -->
        <div class="quick-messages">
            <div class="quick-msg" onclick="setQuickMessage('系统通知', '系统运行正常，所有服务已启动。', 'system')">
                <h4>📢 系统通知</h4>
                <p>系统运行正常消息</p>
            </div>
            <div class="quick-msg" onclick="setQuickMessage('操作成功', '数据备份已完成，系统运行稳定。', 'success')">
                <h4>✅ 成功消息</h4>
                <p>操作成功提示</p>
            </div>
            <div class="quick-msg" onclick="setQuickMessage('警告提醒', '磁盘空间不足，建议清理无用文件。', 'warning')">
                <h4>⚠️ 警告消息</h4>
                <p>警告提醒信息</p>
            </div>
            <div class="quick-msg" onclick="setQuickMessage('错误警报', '检测到系统异常，请立即处理！', 'error')">
                <h4>❌ 错误消息</h4>
                <p>错误警报信息</p>
            </div>
            <div class="quick-msg" onclick="setQuickMessage('信息提示', '今日访问量已达到新高，系统表现良好。', 'info')">
                <h4>💡 信息消息</h4>
                <p>信息提示内容</p>
            </div>
        </div>

        <form id="pushForm">
            <div class="form-group">
                <label for="title">通知标题</label>
                <input type="text" id="title" name="title" placeholder="请输入通知标题" required>
            </div>
            
            <div class="form-group">
                <label for="content">通知内容</label>
                <textarea id="content" name="content" placeholder="请输入通知内容" required></textarea>
            </div>
            
            <div class="form-group">
                <label for="type">通知类型</label>
                <select id="type" name="type">
                    <option value="system">系统通知</option>
                    <option value="success">成功消息</option>
                    <option value="warning">警告消息</option>
                    <option value="error">错误消息</option>
                    <option value="info">信息消息</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="url">跳转链接（可选）</label>
                <input type="url" id="url" name="url" placeholder="点击通知后跳转的URL">
            </div>
            
            <div class="form-group">
                <label for="method">推送方式</label>
                <select id="method" name="method">
                    <option value="both">双重推送（推荐）</option>
                    <option value="redis">Redis发布订阅</option>
                    <option value="websocket">WebSocket直推</option>
                </select>
            </div>
        </form>

        <div class="btn-group">
            <button type="button" class="btn-primary" onclick="pushMessage()">🚀 推送消息</button>
            <button type="button" class="btn-success" onclick="pushSuccess()">✅ 推送成功</button>
            <button type="button" class="btn-warning" onclick="pushWarning()">⚠️ 推送警告</button>
            <button type="button" class="btn-danger" onclick="pushError()">❌ 推送错误</button>
            <button type="button" class="btn-info" onclick="pushInfo()">💡 推送信息</button>
            <button type="button" class="btn-secondary" onclick="testConnection()">🔧 测试连接</button>
        </div>

        <div class="btn-group" style="margin-top: 10px;">
            <button type="button" class="btn-secondary" onclick="getOnlineAdmins()">👥 在线管理员</button>
            <button type="button" class="btn-secondary" onclick="debugConnections()">🔍 调试连接</button>
            <button type="button" class="btn-secondary" onclick="rebuildConnection()">🔧 重建连接</button>
            <button type="button" class="btn-secondary" onclick="testDirectPush()">📡 Redis订阅测试</button>
        </div>

        <div class="btn-group" style="margin-top: 10px;">
            <button type="button" class="btn-secondary" onclick="checkRedisSubscribe()">🔍 检查Redis订阅</button>
            <button type="button" class="btn-secondary" onclick="testRoomPush()">🏠 测试Room推送</button>
            <button type="button" class="btn-secondary" onclick="batchPush()">📦 批量推送</button>
            <button type="button" class="btn-secondary" onclick="clearResult()">🗑️ 清空结果</button>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        // API基础URL，请根据实际情况修改
        const API_BASE = '/api/admin_push';

        // 设置快速消息
        function setQuickMessage(title, content, type) {
            document.getElementById('title').value = title;
            document.getElementById('content').value = content;
            document.getElementById('type').value = type;
        }

        // 显示结果
        function showResult(data, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            resultDiv.textContent = JSON.stringify(data, null, 2);
        }

        // 清空结果
        function clearResult() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
            resultDiv.textContent = '';
        }

        // 通用API请求函数
        async function apiRequest(endpoint, data = {}) {
            try {
                const response = await fetch(API_BASE + endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                showResult(result, result.code !== 1);
                return result;
            } catch (error) {
                showResult({ error: error.message }, true);
                return null;
            }
        }

        // 推送消息
        async function pushMessage() {
            const formData = {
                title: document.getElementById('title').value,
                content: document.getElementById('content').value,
                type: document.getElementById('type').value,
                url: document.getElementById('url').value,
                method: document.getElementById('method').value
            };

            if (!formData.title || !formData.content) {
                alert('请填写标题和内容');
                return;
            }

            await apiRequest('/pushMessage', formData);
        }

        // 推送成功消息
        async function pushSuccess() {
            await apiRequest('/pushSuccess', {
                content: document.getElementById('content').value || '操作已成功完成！'
            });
        }

        // 推送警告消息
        async function pushWarning() {
            await apiRequest('/pushWarning', {
                content: document.getElementById('content').value || '请注意系统警告信息！'
            });
        }

        // 推送错误消息
        async function pushError() {
            await apiRequest('/pushError', {
                content: document.getElementById('content').value || '系统发生错误，请立即处理！'
            });
        }

        // 推送信息消息
        async function pushInfo() {
            await apiRequest('/pushInfo', {
                content: document.getElementById('content').value || '这是一条信息提示。'
            });
        }

        // 测试连接
        async function testConnection() {
            try {
                const response = await fetch(API_BASE + '/testConnection');
                const result = await response.json();
                showResult(result, result.code !== 1);
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        // 获取在线管理员
        async function getOnlineAdmins() {
            try {
                const response = await fetch(API_BASE + '/getOnlineAdmins');
                const result = await response.json();
                showResult(result, result.code !== 1);
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        // 批量推送示例
        async function batchPush() {
            const messages = [
                { title: '批量消息1', content: '这是第一条批量消息', type: 'system' },
                { title: '批量消息2', content: '这是第二条批量消息', type: 'info' },
                { title: '批量消息3', content: '这是第三条批量消息', type: 'success' }
            ];

            await apiRequest('/batchPush', { messages });
        }

        // 调试连接信息
        async function debugConnections() {
            try {
                const response = await fetch(API_BASE + '/debugConnections');
                const result = await response.json();
                showResult(result, result.code !== 1);
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        // 重建管理员连接
        async function rebuildConnection() {
            const adminId = prompt('请输入管理员ID:', '1');
            const fd = prompt('请输入连接标识符(fd):', '1');
            const nickname = prompt('请输入管理员昵称:', 'admin');
            
            if (!adminId || !fd) {
                alert('管理员ID和连接标识符不能为空');
                return;
            }

            await apiRequest('/rebuildAdminConnection', {
                admin_id: parseInt(adminId),
                fd: parseInt(fd),
                nickname: nickname
            });
        }

        // 测试Redis订阅推送
        async function testDirectPush() {
            await apiRequest('/testDirectPush', {
                title: 'Redis订阅测试',
                content: '这是一条Redis订阅测试消息，时间: ' + new Date().toLocaleTimeString()
            });
        }

        // 检查Redis订阅状态
        async function checkRedisSubscribe() {
            try {
                const response = await fetch(API_BASE + '/checkRedisSubscribe');
                const result = await response.json();
                showResult(result, result.code !== 1);
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        // 测试Room推送
        async function testRoomPush() {
            await apiRequest('/testRoomPush', {
                title: 'Room推送测试',
                content: '这是一条Room推送测试消息，时间: ' + new Date().toLocaleTimeString()
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认值
            document.getElementById('title').value = '测试通知';
            document.getElementById('content').value = '这是一条测试推送消息，用于验证后台管理员通知功能是否正常工作。';
        });
    </script>
</body>
</html>