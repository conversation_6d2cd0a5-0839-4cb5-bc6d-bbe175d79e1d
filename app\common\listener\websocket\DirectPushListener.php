<?php
namespace app\common\listener\websocket;

use app\common\websocket\CombinedHandler;
use think\facade\Log;

class DirectPushListener
{
    /**
     * @var CombinedHandler
     */
    protected $handler;

    public function __construct(CombinedHandler $handler)
    {
        $this->handler = $handler;
    }

    /**
     * 处理直接推送事件
     *
     * @return bool
     */
    public function handle($data): bool
    {
        if (is_string($data)) {
            $data = json_decode($data, true);
        }

        Log::info('收到直接推送消息: ' . json_encode($data));

        try {
            if (isset($data['event']) && $data['event'] === 'notification' && isset($data['data'])) {
                $notificationData = $data['data'];
                $targetFd = $data['target_fd'] ?? null;

                if ($targetFd) {
                    // 推送给特定的fd
                    $result = $this->handler->pushData($targetFd, 'notification', $notificationData);
                    Log::info("直接推送给fd {$targetFd}: " . ($result ? '成功' : '失败'));
                    return $result;
                } else {
                    // 推送给所有管理员
                    return $this->handler->sendNotificationToAdmin(
                        $notificationData['title'],
                        $notificationData['content'],
                        $notificationData['type'] ?? 'admin_notification',
                        $notificationData['url'] ?? '',
                        $notificationData['icon'] ?? 0
                    );
                }
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('直接推送处理失败: ' . $e->getMessage());
            return false;
        }
    }
}