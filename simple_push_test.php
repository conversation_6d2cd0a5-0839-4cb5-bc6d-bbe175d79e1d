<?php
/**
 * 简化推送测试脚本
 */

$apiBase = 'http://your-domain.com/api/admin_push';

function testAPI($endpoint, $data = null) {
    global $apiBase;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiBase . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

echo "🚀 简化推送测试\n";
echo str_repeat("=", 50) . "\n";

// 1. 重建管理员连接
echo "1. 重建管理员连接...\n";
$result = testAPI('/rebuildAdminConnection', [
    'admin_id' => 1,
    'nickname' => 'admin',
    'fd' => 1
]);

if ($result['data']['code'] == 1) {
    echo "✅ 重建成功\n";
} else {
    echo "❌ 重建失败: " . ($result['data']['msg'] ?? 'unknown') . "\n";
}

sleep(1);

// 2. 调试连接
echo "\n2. 检查连接状态...\n";
$result = testAPI('/debugConnections');
if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    $adminConnections = $data['admin_connections'] ?? [];
    
    foreach ($adminConnections as $key => $conn) {
        echo "- {$key}: ";
        if ($conn['parsed_data']) {
            echo "✅ 有数据 - " . json_encode($conn['parsed_data']) . "\n";
        } else {
            echo "❌ 无数据 (TTL: " . ($conn['ttl'] ?? 'unknown') . ")\n";
        }
    }
} else {
    echo "❌ 调试失败\n";
}

sleep(1);

// 3. 测试直接推送
echo "\n3. 测试直接推送...\n";
$result = testAPI('/testDirectPush', [
    'fd' => 1,
    'title' => '测试推送',
    'content' => '这是一条测试推送消息 - ' . date('H:i:s')
]);

if ($result['data']['code'] == 1) {
    echo "✅ 推送成功\n";
    $data = $result['data']['data'];
    echo "- 订阅推送结果: " . ($data['subscribe_result'] ?? 0) . " 个订阅者\n";
    echo "- 直接推送结果: " . ($data['direct_result'] ?? 0) . " 个订阅者\n";
} else {
    echo "❌ 推送失败: " . ($result['data']['msg'] ?? 'unknown') . "\n";
}

sleep(1);

// 4. 测试常规推送
echo "\n4. 测试常规推送...\n";
$result = testAPI('/pushMessage', [
    'title' => '常规推送测试',
    'content' => '这是一条常规推送消息 - ' . date('H:i:s'),
    'type' => 'system',
    'method' => 'both'
]);

if ($result['data']['code'] == 1) {
    echo "✅ 常规推送成功\n";
    $data = $result['data']['data'];
    
    if (isset($data['websocket'])) {
        echo "- WebSocket推送: " . ($data['websocket']['success'] ? '成功' : '失败') . "\n";
    }
    
    if (isset($data['redis'])) {
        echo "- Redis推送: " . ($data['redis']['success'] ? '成功' : '失败') . "\n";
        echo "  订阅者: " . ($data['redis']['subscribers'] ?? 0) . "\n";
    }
} else {
    echo "❌ 常规推送失败: " . ($result['data']['msg'] ?? 'unknown') . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "✅ 测试完成！请检查后台是否收到推送消息。\n";
?>