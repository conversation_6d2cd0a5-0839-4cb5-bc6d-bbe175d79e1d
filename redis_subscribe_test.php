<?php
/**
 * Redis订阅功能测试脚本
 */

$apiBase = 'http://your-domain.com/api/admin_push';

function testAPI($endpoint, $data = null) {
    global $apiBase;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiBase . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

echo "📡 Redis订阅功能测试\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";

// 1. 检查Redis订阅状态
echo "1. 检查Redis订阅状态...\n";
$result = testAPI('/checkRedisSubscribe');

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    
    echo "✅ Redis连接: " . ($data['redis_connection'] ? '正常' : '失败') . "\n";
    
    if (isset($data['redis_info']['connected_clients'])) {
        echo "- 连接客户端数: " . $data['redis_info']['connected_clients'] . "\n";
    }
    
    echo "Swoole配置:\n";
    $swooleConfig = $data['swoole_config'];
    echo "- Handler: " . $swooleConfig['handler'] . "\n";
    echo "- Room类型: " . $swooleConfig['room_type'] . "\n";
    echo "- Redis主机: " . $swooleConfig['redis_host'] . "\n";
    
    echo "订阅频道:\n";
    $subscribeChannels = $swooleConfig['subscribe_channels'];
    foreach ($subscribeChannels as $channel => $listener) {
        echo "- {$channel}: {$listener}\n";
    }
    
    echo "发布测试:\n";
    $publishTest = $data['publish_test'];
    echo "- 频道: " . $publishTest['channel'] . "\n";
    echo "- 订阅者数量: " . $publishTest['subscribers'] . "\n";
    echo "- 结果: " . ($publishTest['success'] ? '✅ 成功' : '❌ 失败') . "\n";
    
    if (!$publishTest['success']) {
        echo "\n⚠️  没有订阅者收到消息，可能的原因:\n";
        echo "1. Swoole服务器没有启动\n";
        echo "2. Swoole服务器没有正确订阅Redis频道\n";
        echo "3. Redis连接配置不匹配\n";
        echo "4. 需要重启Swoole服务器\n";
    }
    
} else {
    echo "❌ Redis订阅状态检查失败: " . ($result['data']['msg'] ?? 'unknown') . "\n";
}

echo "\n" . str_repeat("-", 60) . "\n";

// 2. 重建管理员连接
echo "2. 重建管理员连接...\n";
$result = testAPI('/rebuildAdminConnection', [
    'admin_id' => 1,
    'nickname' => 'admin',
    'fd' => 1
]);

if ($result['data']['code'] == 1) {
    echo "✅ 管理员连接重建成功\n";
} else {
    echo "❌ 管理员连接重建失败\n";
}

echo "\n" . str_repeat("-", 60) . "\n";

// 3. 测试Redis订阅推送
echo "3. 测试Redis订阅推送...\n";
$result = testAPI('/testDirectPush', [
    'title' => 'Redis订阅测试',
    'content' => '这是Redis订阅功能测试消息 - ' . date('H:i:s')
]);

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    $subscribers = $data['publish_result'] ?? 0;
    
    if ($subscribers > 0) {
        echo "✅ Redis订阅推送成功！订阅者: {$subscribers}\n";
        echo "请检查后台管理员页面是否收到消息\n";
    } else {
        echo "❌ Redis订阅推送失败，没有订阅者\n";
        echo "\n🔧 解决方案:\n";
        echo "1. 重启Swoole服务器\n";
        echo "2. 检查Swoole配置中的subscribe设置\n";
        echo "3. 确认Redis连接配置正确\n";
    }
} else {
    echo "❌ Redis订阅推送测试失败: " . ($result['data']['msg'] ?? 'unknown') . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "📋 测试总结:\n";
echo "1. 如果订阅者数量为0，说明Swoole服务器没有订阅Redis频道\n";
echo "2. 请重启Swoole服务器，确保订阅配置生效\n";
echo "3. 重启后再次运行此测试脚本\n";
echo "\n✅ Redis订阅测试完成！\n";

// 4. 提供重启命令提示
echo "\n🚀 Swoole服务器重启命令参考:\n";
echo "# 停止Swoole服务器\n";
echo "php think swoole:server stop\n";
echo "\n# 启动Swoole服务器\n";
echo "php think swoole:server start\n";
echo "\n# 或者重启\n";
echo "php think swoole:server restart\n";
?>