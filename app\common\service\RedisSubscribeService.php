<?php

namespace app\common\service;

use think\facade\Log;
use think\facade\Cache;

/**
 * Redis订阅服务
 * 用于处理Redis发布订阅消息
 */
class RedisSubscribeService
{
    /**
     * 启动Redis订阅监听
     */
    public static function startSubscribe()
    {
        try {
            // 使用协程方式启动Redis订阅
            \Swoole\Coroutine::create(function () {
                self::subscribeLoop();
            });
            
            Log::info('Redis订阅服务启动成功');
            
        } catch (\Exception $e) {
            Log::error('Redis订阅服务启动失败: ' . $e->getMessage());
        }
    }

    /**
     * Redis订阅循环
     */
    protected static function subscribeLoop()
    {
        try {
            // 创建Redis连接
            $redis = new \Redis();
            $redisConfig = config('swoole.websocket.room.redis');
            
            if (!$redis->connect($redisConfig['host'], $redisConfig['port'])) {
                Log::error('Redis订阅连接失败');
                return;
            }
            
            if (isset($redisConfig['database'])) {
                $redis->select($redisConfig['database']);
            }
            
            Log::info('Redis订阅循环启动，开始订阅频道');
            
            // 订阅频道
            $redis->subscribe(['admin_notification', 'admin_direct_push'], function ($redis, $channel, $message) {
                Log::info("收到Redis订阅消息: channel={$channel}, message={$message}");
                
                try {
                    $data = json_decode($message, true);
                    if (!$data) {
                        Log::error('Redis订阅消息JSON解析失败: ' . $message);
                        return;
                    }
                    
                    // 根据频道处理不同的消息
                    if ($channel === 'admin_notification') {
                        self::handleAdminNotification($data);
                    } elseif ($channel === 'admin_direct_push') {
                        self::handleDirectPush($data);
                    }
                    
                } catch (\Exception $e) {
                    Log::error('Redis订阅消息处理失败: ' . $e->getMessage());
                }
            });
            
        } catch (\Exception $e) {
            Log::error('Redis订阅循环异常: ' . $e->getMessage());
            // 等待一段时间后重试
            \Swoole\Coroutine::sleep(5);
            self::subscribeLoop();
        }
    }

    /**
     * 处理管理员通知消息
     */
    protected static function handleAdminNotification($data)
    {
        if (isset($data['event']) && $data['event'] === 'admin_notification' && isset($data['data'])) {
            $notificationData = $data['data'];
            
            // 获取CombinedHandler实例
            $handler = app(\app\common\websocket\CombinedHandler::class);
            $handler->sendNotificationToAdmin(
                $notificationData['title'],
                $notificationData['content'],
                $notificationData['type'] ?? 'admin_notification',
                $notificationData['url'] ?? '',
                $notificationData['icon'] ?? 0
            );
        }
    }

    /**
     * 处理直接推送消息
     */
    protected static function handleDirectPush($data)
    {
        if (isset($data['event']) && $data['event'] === 'notification' && isset($data['data'])) {
            $notificationData = $data['data'];
            $targetFd = $data['target_fd'] ?? null;

            // 获取CombinedHandler实例
            $handler = app(\app\common\websocket\CombinedHandler::class);
            
            if ($targetFd) {
                // 推送给特定的fd
                $handler->pushData($targetFd, 'notification', $notificationData);
            } else {
                // 推送给所有管理员
                $handler->sendNotificationToAdmin(
                    $notificationData['title'],
                    $notificationData['content'],
                    $notificationData['type'] ?? 'admin_notification',
                    $notificationData['url'] ?? '',
                    $notificationData['icon'] ?? 0
                );
            }
        }
    }
}