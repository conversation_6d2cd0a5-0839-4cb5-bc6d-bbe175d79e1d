<?php

namespace app\common\service;

use think\facade\Log;
use think\facade\Cache;

/**
 * 管理员通知服务
 * 简化的Redis推送服务，提供便捷的调用方法
 */
class AdminNotificationService
{
    /**
     * 推送通知给所有在线管理员
     * 
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @param string $type 通知类型：system, success, error, warning, info
     * @param string $url 点击跳转URL
     * @param int $icon 图标类型：0-默认，1-成功，2-错误，3-警告，4-信息
     * @return bool 推送是否成功
     */
    public static function push($title, $content, $type = 'system', $url = '', $icon = 0)
    {
        try {
            // 构建通知数据
            $notificationData = [
                'type' => $type,
                'title' => $title,
                'content' => $content,
                'url' => $url,
                'icon' => $icon,
                'timestamp' => time()
            ];

            // 构建Redis消息
            $message = [
                'event' => 'admin_notification',
                'data' => $notificationData
            ];

            // 发布到Redis频道
            $redis = Cache::store('redis');
            $result = $redis->publish('admin_notification', json_encode($message, JSON_UNESCAPED_UNICODE));

            Log::info("管理员通知推送: title={$title}, subscribers={$result}");
            
            return $result > 0;

        } catch (\Exception $e) {
            Log::error('管理员通知推送失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 推送系统通知
     */
    public static function system($title, $content, $url = '')
    {
        return self::push($title, $content, 'system', $url, 0);
    }

    /**
     * 推送成功通知
     */
    public static function success($title, $content, $url = '')
    {
        return self::push($title, $content, 'success', $url, 1);
    }

    /**
     * 推送错误通知
     */
    public static function error($title, $content, $url = '')
    {
        return self::push($title, $content, 'error', $url, 2);
    }

    /**
     * 推送警告通知
     */
    public static function warning($title, $content, $url = '')
    {
        return self::push($title, $content, 'warning', $url, 3);
    }

    /**
     * 推送信息通知
     */
    public static function info($title, $content, $url = '')
    {
        return self::push($title, $content, 'info', $url, 4);
    }

    /**
     * 推送给特定管理员
     * 
     * @param int $adminId 管理员ID
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @param string $type 通知类型
     * @param string $url 点击跳转URL
     * @param int $icon 图标类型
     * @return bool 推送是否成功
     */
    public static function pushToAdmin($adminId, $title, $content, $type = 'system', $url = '', $icon = 0)
    {
        try {
            // 获取管理员的连接fd
            $redis = Cache::store('redis');
            $websocketPrefix = config('default.websocket_prefix', 'socket_');
            $adminPrefix = $websocketPrefix . 'admin_';
            $adminKey = $adminPrefix . 'admin_' . $adminId;
            
            $fd = $redis->get($adminKey);
            if (!$fd) {
                Log::warning("管理员 {$adminId} 不在线，无法推送");
                return false;
            }

            // 构建通知数据
            $notificationData = [
                'type' => $type,
                'title' => $title,
                'content' => $content,
                'url' => $url,
                'icon' => $icon,
                'timestamp' => time()
            ];

            // 构建直接推送消息
            $message = [
                'event' => 'notification',
                'data' => $notificationData,
                'target_fd' => $fd
            ];

            // 发布到直接推送频道
            $result = $redis->publish('admin_direct_push', json_encode($message, JSON_UNESCAPED_UNICODE));

            Log::info("推送给管理员 {$adminId} (fd={$fd}): title={$title}, result={$result}");
            
            return $result > 0;

        } catch (\Exception $e) {
            Log::error("推送给管理员 {$adminId} 失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量推送通知
     * 
     * @param array $notifications 通知列表，每个元素包含title, content等字段
     * @return array 推送结果统计
     */
    public static function batchPush($notifications)
    {
        $results = [
            'total' => count($notifications),
            'success' => 0,
            'failed' => 0,
            'details' => []
        ];

        foreach ($notifications as $index => $notification) {
            try {
                $title = $notification['title'] ?? '批量通知';
                $content = $notification['content'] ?? '';
                $type = $notification['type'] ?? 'system';
                $url = $notification['url'] ?? '';
                $icon = $notification['icon'] ?? 0;

                if (empty($content)) {
                    $results['failed']++;
                    $results['details'][$index] = ['success' => false, 'error' => '内容不能为空'];
                    continue;
                }

                $success = self::push($title, $content, $type, $url, $icon);
                
                if ($success) {
                    $results['success']++;
                    $results['details'][$index] = ['success' => true];
                } else {
                    $results['failed']++;
                    $results['details'][$index] = ['success' => false, 'error' => '推送失败'];
                }

                // 避免推送过快
                usleep(100000); // 0.1秒

            } catch (\Exception $e) {
                $results['failed']++;
                $results['details'][$index] = ['success' => false, 'error' => $e->getMessage()];
            }
        }

        return $results;
    }

    /**
     * 获取在线管理员数量
     */
    public static function getOnlineAdminCount()
    {
        try {
            $redis = Cache::store('redis');
            $websocketPrefix = config('default.websocket_prefix', 'socket_');
            $adminPrefix = $websocketPrefix . 'admin_';
            
            $adminKeys = $redis->keys($adminPrefix . 'fd_*');
            $count = 0;
            
            if (is_array($adminKeys)) {
                foreach ($adminKeys as $key) {
                    $adminData = $redis->get($key);
                    if (!empty($adminData)) {
                        $data = json_decode($adminData, true);
                        if ($data && isset($data['type']) && $data['type'] === 'admin') {
                            $count++;
                        }
                    }
                }
            }
            
            return $count;

        } catch (\Exception $e) {
            Log::error('获取在线管理员数量失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 检查Redis连接状态
     */
    public static function checkConnection()
    {
        try {
            $redis = Cache::store('redis');
            $redis->ping();
            return true;
        } catch (\Exception $e) {
            Log::error('Redis连接检查失败: ' . $e->getMessage());
            return false;
        }
    }
}