<?php
/**
 * 后台管理员推送测试脚本
 * 使用方法: php test_admin_push.php
 */

// 设置API基础URL，请根据实际情况修改
$apiBase = 'http://your-domain.com/api/admin_push';

// 测试数据
$testCases = [
    [
        'name' => '调试连接信息',
        'url' => '/debugConnections',
        'method' => 'GET',
        'data' => null
    ],
    [
        'name' => '获取在线管理员',
        'url' => '/getOnlineAdmins',
        'method' => 'GET',
        'data' => null
    ],
    [
        'name' => '测试连接状态',
        'url' => '/testConnection',
        'method' => 'GET',
        'data' => null
    ],
    [
        'name' => '推送系统通知',
        'url' => '/pushSystemNotice',
        'method' => 'POST',
        'data' => [
            'title' => '测试系统通知',
            'content' => '这是一条测试系统通知消息，用于验证推送功能是否正常。'
        ]
    ],
    [
        'name' => '推送成功消息',
        'url' => '/pushSuccess',
        'method' => 'POST',
        'data' => [
            'content' => '测试操作已成功完成！'
        ]
    ],
    [
        'name' => '推送警告消息',
        'url' => '/pushWarning',
        'method' => 'POST',
        'data' => [
            'content' => '这是一条测试警告消息。'
        ]
    ],
    [
        'name' => '推送错误消息',
        'url' => '/pushError',
        'method' => 'POST',
        'data' => [
            'content' => '这是一条测试错误消息。'
        ]
    ],
    [
        'name' => '推送信息消息',
        'url' => '/pushInfo',
        'method' => 'POST',
        'data' => [
            'content' => '这是一条测试信息消息。'
        ]
    ],
    [
        'name' => '自定义推送消息',
        'url' => '/pushMessage',
        'method' => 'POST',
        'data' => [
            'title' => '自定义测试通知',
            'content' => '这是一条自定义的测试通知消息，包含完整的参数。',
            'type' => 'system',
            'url' => 'https://example.com',
            'icon' => 0,
            'method' => 'both'
        ]
    ]
];

/**
 * 发送HTTP请求
 */
function sendRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen(json_encode($data))
            ]);
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return [
            'success' => false,
            'error' => $error,
            'http_code' => $httpCode
        ];
    }
    
    return [
        'success' => true,
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

/**
 * 格式化输出结果
 */
function formatOutput($testName, $result) {
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "测试: {$testName}\n";
    echo str_repeat("-", 60) . "\n";
    
    if (!$result['success']) {
        echo "❌ 请求失败: " . $result['error'] . "\n";
        return;
    }
    
    echo "HTTP状态码: " . $result['http_code'] . "\n";
    
    if ($result['data']) {
        echo "响应结果:\n";
        echo json_encode($result['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        if (isset($result['data']['code'])) {
            $status = $result['data']['code'] == 1 ? "✅ 成功" : "❌ 失败";
            echo "状态: {$status}\n";
            if (isset($result['data']['msg'])) {
                echo "消息: " . $result['data']['msg'] . "\n";
            }
        }
    } else {
        echo "原始响应:\n" . $result['response'] . "\n";
    }
}

// 主程序
echo "🚀 后台管理员推送功能测试\n";
echo "API基础URL: {$apiBase}\n";
echo "开始时间: " . date('Y-m-d H:i:s') . "\n";

foreach ($testCases as $index => $testCase) {
    $url = $apiBase . $testCase['url'];
    $result = sendRequest($url, $testCase['method'], $testCase['data']);
    formatOutput($testCase['name'], $result);
    
    // 在推送测试之间添加延迟，避免过快
    if (strpos($testCase['url'], 'push') !== false) {
        sleep(1);
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "✅ 所有测试完成！\n";
echo "结束时间: " . date('Y-m-d H:i:s') . "\n";
echo "\n请检查后台管理员页面是否收到通知消息。\n";
echo "如果没有收到消息，请:\n";
echo "1. 确认管理员已登录后台并建立WebSocket连接\n";
echo "2. 检查Swoole服务器是否正常运行\n";
echo "3. 检查Redis服务是否正常\n";
echo "4. 查看系统日志获取详细错误信息\n";
?>