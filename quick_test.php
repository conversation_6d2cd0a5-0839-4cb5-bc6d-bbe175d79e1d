<?php
/**
 * 快速测试脚本 - 检查配置和Redis键值
 */

// 设置API基础URL，请根据实际情况修改
$apiBase = 'http://your-domain.com/api/admin_push';

function sendRequest($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error, 'http_code' => $httpCode];
    }
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

echo "🚀 快速测试 - 检查管理员连接状态\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";

// 1. 调试连接信息
echo "1. 调试连接信息...\n";
$result = sendRequest($apiBase . '/debugConnections');
if (isset($result['data'])) {
    $data = $result['data'];
    if ($data['code'] == 1) {
        echo "✅ 调试信息获取成功\n";
        
        $debugData = $data['data'];
        echo "配置信息:\n";
        echo "- websocket_prefix: " . ($debugData['config']['websocket_prefix'] ?? 'null') . "\n";
        echo "- admin_prefix: " . ($debugData['config']['admin_prefix'] ?? 'null') . "\n";
        
        echo "Redis键值:\n";
        echo "- admin_fd_keys: " . json_encode($debugData['redis_keys']['admin_fd_keys'] ?? []) . "\n";
        echo "- admin_id_keys: " . json_encode($debugData['redis_keys']['admin_id_keys'] ?? []) . "\n";
        
        echo "管理员连接:\n";
        $adminConnections = $debugData['admin_connections'] ?? [];
        if (empty($adminConnections)) {
            echo "❌ 没有找到管理员连接\n";
        } else {
            foreach ($adminConnections as $key => $conn) {
                echo "- {$key}: " . json_encode($conn['parsed_data']) . "\n";
            }
        }
        
        echo "Room信息:\n";
        $roomInfo = $debugData['room_info'] ?? [];
        if (isset($roomInfo['admin_group'])) {
            echo "- admin_group: " . json_encode($roomInfo['admin_group']) . "\n";
        }
        
    } else {
        echo "❌ 调试信息获取失败: " . ($data['msg'] ?? 'unknown error') . "\n";
    }
} else {
    echo "❌ 请求失败: " . ($result['error'] ?? 'unknown error') . "\n";
}

echo "\n" . str_repeat("-", 60) . "\n";

// 2. 获取在线管理员
echo "2. 获取在线管理员...\n";
$result = sendRequest($apiBase . '/getOnlineAdmins');
if (isset($result['data'])) {
    $data = $result['data'];
    if ($data['code'] == 1) {
        echo "✅ 在线管理员信息获取成功\n";
        $adminData = $data['data'];
        
        if (isset($adminData['total_count'])) {
            echo "在线管理员数量: " . $adminData['total_count'] . "\n";
        }
        
        if (isset($adminData['fd_method']) && !empty($adminData['fd_method'])) {
            echo "通过fd方法找到的管理员:\n";
            foreach ($adminData['fd_method'] as $admin) {
                echo "- ID: {$admin['admin_id']}, 昵称: {$admin['nickname']}, FD: {$admin['fd']}\n";
            }
        }
        
        if (isset($adminData['id_method']) && !empty($adminData['id_method'])) {
            echo "通过id方法找到的管理员:\n";
            foreach ($adminData['id_method'] as $admin) {
                echo "- ID: {$admin['admin_id']}, FD: {$admin['fd']}\n";
            }
        }
        
        if (isset($adminData['room_method'])) {
            echo "Room方法结果: " . json_encode($adminData['room_method']) . "\n";
        }
        
    } else {
        echo "❌ 获取在线管理员失败: " . ($data['msg'] ?? 'unknown error') . "\n";
    }
} else {
    echo "❌ 请求失败: " . ($result['error'] ?? 'unknown error') . "\n";
}

echo "\n" . str_repeat("-", 60) . "\n";

// 3. 测试推送
echo "3. 测试推送消息...\n";
$pushData = [
    'title' => '测试推送',
    'content' => '这是一条测试推送消息，时间: ' . date('H:i:s'),
    'type' => 'system',
    'method' => 'both'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiBase . '/pushMessage');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($pushData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['code'] == 1) {
        echo "✅ 推送消息成功\n";
        $pushResult = $data['data'] ?? [];
        
        if (isset($pushResult['websocket'])) {
            echo "WebSocket推送: " . ($pushResult['websocket']['success'] ? '成功' : '失败') . "\n";
            if (isset($pushResult['websocket']['direct_result'])) {
                $directResult = $pushResult['websocket']['direct_result'];
                echo "- 找到管理员数量: " . ($directResult['admin_count'] ?? 0) . "\n";
                echo "- 推送成功数量: " . ($directResult['success_count'] ?? 0) . "\n";
            }
        }
        
        if (isset($pushResult['redis'])) {
            echo "Redis推送: " . ($pushResult['redis']['success'] ? '成功' : '失败') . "\n";
            echo "- 订阅者数量: " . ($pushResult['redis']['subscribers'] ?? 0) . "\n";
        }
        
    } else {
        echo "❌ 推送消息失败: " . ($data['msg'] ?? 'unknown error') . "\n";
    }
} else {
    echo "❌ 推送请求失败\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "✅ 快速测试完成\n";
echo "请检查后台管理员页面是否收到推送消息\n";
?>