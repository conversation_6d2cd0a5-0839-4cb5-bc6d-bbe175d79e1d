<?php
/**
 * Git Webhook 自动部署脚本
 * 放在服务器上，当收到git推送通知时自动部署
 */

// 安全验证密钥（建议设置一个复杂的密钥）
$secret = 'your_webhook_secret_key_here';

// 获取请求数据
$payload = file_get_contents('php://input');
$signature = $_SERVER['HTTP_X_GITEE_TOKEN'] ?? $_SERVER['HTTP_X_HUB_SIGNATURE'] ?? '';

// 验证签名（如果设置了密钥）
if ($secret && !hash_equals($secret, $signature)) {
    http_response_code(403);
    die('Forbidden');
}

// 解析payload
$data = json_decode($payload, true);

// 检查是否是develop分支的推送
if (isset($data['ref']) && $data['ref'] === 'refs/heads/develop') {
    
    // 记录日志
    $log = date('Y-m-d H:i:s') . " - 收到develop分支推送，开始自动部署\n";
    file_put_contents('/tmp/deploy.log', $log, FILE_APPEND);
    
    // 执行部署脚本
    $output = shell_exec('cd /www/wwwroot/dev.huohanghang.cn && bash scripts/deploy-dev.sh 2>&1');
    
    // 记录部署结果
    $log = date('Y-m-d H:i:s') . " - 部署完成: " . $output . "\n";
    file_put_contents('/tmp/deploy.log', $log, FILE_APPEND);
    
    echo "部署成功";
} else {
    echo "非develop分支推送，忽略";
}
?>