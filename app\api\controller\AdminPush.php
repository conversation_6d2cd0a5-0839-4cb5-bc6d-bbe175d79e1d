<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\common\websocket\CombinedHandler;
use think\facade\Request;
use think\facade\Log;
use think\facade\Cache;
use app\common\server\JsonServer;

/**
 * 后台管理员推送消息控制器
 * 专门用于向后台管理员推送实时通知消息
 */
class AdminPush extends Api
{
    /**
     * 不需要登录验证的方法
     * @var array
     */
    public $like_not_need_login = [
        'pushMessage',
        'pushSystemNotice',
        'pushWarning',
        'pushError',
        'pushSuccess',
        'pushInfo',
        'testConnection',
        'getOnlineAdmins',
        'batchPush',
        'debugConnections',
        'rebuildAdminConnection',
        'testDirectPush',
        'testRoomPush',
        'checkRedisSubscribe'
    ];

    /**
     * 推送消息给后台管理员
     * @ApiTitle (推送消息给后台管理员)
     * @ApiSummary (向所有在线的后台管理员推送实时通知消息)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":true, "desc":"通知标题"},
     *   {"name":"content", "type":"string", "require":true, "desc":"通知内容"},
     *   {"name":"type", "type":"string", "require":false, "desc":"通知类型：system, success, error, warning, info，默认为system"},
     *   {"name":"url", "type":"string", "require":false, "desc":"点击通知跳转的URL，默认为空"},
     *   {"name":"icon", "type":"integer", "require":false, "desc":"通知图标：0-默认，1-成功，2-错误，3-警告，4-信息，默认为0"}
     * )
     * @ApiReturn ({"code":1,"msg":"推送成功","data":{}})
     */
    public function pushMessage()
    {
        try {
            // 获取请求参数
            $title = Request::param('title', '');
            $content = Request::param('content', '');
            $type = Request::param('type', 'system');
            $url = Request::param('url', '');
            $icon = Request::param('icon', 0, 'intval');

            // 验证必要参数
            if (empty($title) || empty($content)) {
                return JsonServer::error('标题和内容不能为空');
            }

            // 使用新的通知服务
            $result = \app\common\service\AdminNotificationService::push($title, $content, $type, $url, $icon);

            if ($result) {
                return JsonServer::success('推送成功');
            } else {
                return JsonServer::error('推送失败，可能没有在线管理员');
            }

        } catch (\Exception $e) {
            Log::error('后台管理员推送失败: ' . $e->getMessage());
            return JsonServer::error('推送失败: ' . $e->getMessage());
        }
    }

    /**
     * 推送系统通知
     * @ApiTitle (推送系统通知)
     * @ApiSummary (推送系统类型的通知消息)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'系统通知'"},
     *   {"name":"content", "type":"string", "require":true, "desc":"通知内容"}
     * )
     * @ApiReturn ({"code":1,"msg":"推送成功","data":{}})
     */
    public function pushSystemNotice()
    {
        $title = Request::param('title', '系统通知');
        $content = Request::param('content', '');
        
        if (empty($content)) {
            return JsonServer::error('通知内容不能为空');
        }

        return $this->pushMessage([
            'title' => $title,
            'content' => $content,
            'type' => 'system',
            'icon' => 0
        ]);
    }

    /**
     * 推送警告消息
     * @ApiTitle (推送警告消息)
     * @ApiSummary (推送警告类型的通知消息)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'警告提醒'"},
     *   {"name":"content", "type":"string", "require":true, "desc":"通知内容"}
     * )
     * @ApiReturn ({"code":1,"msg":"推送成功","data":{}})
     */
    public function pushWarning()
    {
        $title = Request::param('title', '警告提醒');
        $content = Request::param('content', '');
        
        if (empty($content)) {
            return JsonServer::error('通知内容不能为空');
        }

        return $this->pushMessage([
            'title' => $title,
            'content' => $content,
            'type' => 'warning',
            'icon' => 3
        ]);
    }

    /**
     * 推送错误消息
     * @ApiTitle (推送错误消息)
     * @ApiSummary (推送错误类型的通知消息)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'错误警报'"},
     *   {"name":"content", "type":"string", "require":true, "desc":"通知内容"}
     * )
     * @ApiReturn ({"code":1,"msg":"推送成功","data":{}})
     */
    public function pushError()
    {
        $title = Request::param('title', '错误警报');
        $content = Request::param('content', '');
        
        if (empty($content)) {
            return JsonServer::error('通知内容不能为空');
        }

        return $this->pushMessage([
            'title' => $title,
            'content' => $content,
            'type' => 'error',
            'icon' => 2
        ]);
    }

    /**
     * 推送成功消息
     * @ApiTitle (推送成功消息)
     * @ApiSummary (推送成功类型的通知消息)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'操作成功'"},
     *   {"name":"content", "type":"string", "require":true, "desc":"通知内容"}
     * )
     * @ApiReturn ({"code":1,"msg":"推送成功","data":{}})
     */
    public function pushSuccess()
    {
        $title = Request::param('title', '操作成功');
        $content = Request::param('content', '');
        
        if (empty($content)) {
            return JsonServer::error('通知内容不能为空');
        }

        return $this->pushMessage([
            'title' => $title,
            'content' => $content,
            'type' => 'success',
            'icon' => 1
        ]);
    }

    /**
     * 推送信息消息
     * @ApiTitle (推送信息消息)
     * @ApiSummary (推送信息类型的通知消息)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'信息提示'"},
     *   {"name":"content", "type":"string", "require":true, "desc":"通知内容"}
     * )
     * @ApiReturn ({"code":1,"msg":"推送成功","data":{}})
     */
    public function pushInfo()
    {
        $title = Request::param('title', '信息提示');
        $content = Request::param('content', '');
        
        if (empty($content)) {
            return JsonServer::error('通知内容不能为空');
        }

        return $this->pushMessage([
            'title' => $title,
            'content' => $content,
            'type' => 'info',
            'icon' => 4
        ]);
    }

    /**
     * 批量推送消息
     * @ApiTitle (批量推送消息)
     * @ApiSummary (批量推送多条通知消息)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"messages", "type":"array", "require":true, "desc":"消息列表，每个消息包含title, content, type等字段"}
     * )
     * @ApiReturn ({"code":1,"msg":"批量推送完成","data":{}})
     */
    public function batchPush()
    {
        try {
            $messages = Request::param('messages', []);
            
            if (empty($messages) || !is_array($messages)) {
                return JsonServer::error('消息列表不能为空');
            }

            $results = [];
            $successCount = 0;
            $failCount = 0;

            foreach ($messages as $index => $message) {
                try {
                    $title = $message['title'] ?? '批量通知';
                    $content = $message['content'] ?? '';
                    $type = $message['type'] ?? 'system';
                    $url = $message['url'] ?? '';
                    $icon = $message['icon'] ?? 0;

                    if (empty($content)) {
                        $results[$index] = ['success' => false, 'error' => '内容不能为空'];
                        $failCount++;
                        continue;
                    }

                    // 推送消息
                    $pushResult = $this->pushViaRedis($title, $content, $type, $url, $icon);
                    
                    if ($pushResult['success']) {
                        $results[$index] = ['success' => true, 'data' => $pushResult];
                        $successCount++;
                    } else {
                        $results[$index] = ['success' => false, 'error' => $pushResult['error'] ?? '推送失败'];
                        $failCount++;
                    }

                    // 避免推送过快，添加小延迟
                    usleep(100000); // 0.1秒

                } catch (\Exception $e) {
                    $results[$index] = ['success' => false, 'error' => $e->getMessage()];
                    $failCount++;
                }
            }

            return JsonServer::success("批量推送完成，成功 {$successCount} 条，失败 {$failCount} 条", [
                'total' => count($messages),
                'success' => $successCount,
                'failed' => $failCount,
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('批量推送失败: ' . $e->getMessage());
            return JsonServer::error('批量推送失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试连接状态
     * @ApiTitle (测试连接状态)
     * @ApiSummary (测试WebSocket服务器和Redis连接状态)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"测试完成","data":{}})
     */
    public function testConnection()
    {
        try {
            $result = [
                'swoole_server' => $this->testSwooleConnection(),
                'redis' => $this->testRedisConnection(),
                'online_admins' => $this->getOnlineAdminCount(),
                'timestamp' => time()
            ];

            return JsonServer::success('连接测试完成', $result);

        } catch (\Exception $e) {
            Log::error('连接测试失败: ' . $e->getMessage());
            return JsonServer::error('连接测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取在线管理员信息
     * @ApiTitle (获取在线管理员)
     * @ApiSummary (获取当前在线的管理员连接信息)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"获取成功","data":{}})
     */
    public function getOnlineAdmins()
    {
        try {
            $onlineAdmins = $this->getOnlineAdminList();
            
            return JsonServer::success('获取在线管理员成功', [
                'admins' => $onlineAdmins,
                'timestamp' => time()
            ]);

        } catch (\Exception $e) {
            Log::error('获取在线管理员失败: ' . $e->getMessage());
            return JsonServer::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 调试WebSocket连接信息
     * @ApiTitle (调试WebSocket连接)
     * @ApiSummary (获取详细的WebSocket连接调试信息)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"调试信息获取成功","data":{}})
     */
    public function debugConnections()
    {
        try {
            $redis = Cache::store('redis');
            $websocketPrefix = config('default.websocket_prefix', 'socket_');
            $adminPrefix = $websocketPrefix . 'admin_';
            
            Log::info("调试连接信息 - websocket_prefix: {$websocketPrefix}, admin_prefix: {$adminPrefix}");
            
            $debugInfo = [
                'config' => [
                    'websocket_prefix' => $websocketPrefix,
                    'admin_prefix' => $adminPrefix,
                    'swoole_host' => env('SWOOLE_HOST', '127.0.0.1'),
                    'swoole_port' => env('SWOOLE_PORT', 20211),
                ],
                'redis_keys' => [
                    'admin_fd_keys' => $redis->keys($adminPrefix . 'fd_*'),
                    'admin_id_keys' => $redis->keys($adminPrefix . 'admin_*'),
                    'all_socket_keys' => $redis->keys($websocketPrefix . '*'),
                    'all_admin_keys' => $redis->keys('*admin*'),
                ],
                'admin_connections' => [],
                'room_info' => [],
            ];

            // 获取管理员连接详情
            $adminFdKeys = $redis->keys($adminPrefix . 'fd_*');
            Log::info("找到管理员fd键: " . json_encode($adminFdKeys));
            
            if (is_array($adminFdKeys)) {
                foreach ($adminFdKeys as $key) {
                    try {
                        $adminData = $redis->get($key);
                        $ttl = $redis->ttl($key); // 获取过期时间
                        $debugInfo['admin_connections'][$key] = [
                            'raw_data' => $adminData,
                            'parsed_data' => $adminData ? json_decode($adminData, true) : null,
                            'ttl' => $ttl,
                            'exists' => $redis->exists($key)
                        ];
                        
                        // 如果值为空，尝试重新设置
                        if (empty($adminData)) {
                            Log::warning("管理员fd键 {$key} 值为空，TTL: {$ttl}");
                        }
                    } catch (\Exception $e) {
                        $debugInfo['admin_connections'][$key] = [
                            'error' => $e->getMessage()
                        ];
                    }
                }
            }

            // 获取管理员ID键详情
            $adminIdKeys = $redis->keys($adminPrefix . 'admin_*');
            Log::info("找到管理员id键: " . json_encode($adminIdKeys));
            
            $debugInfo['admin_id_connections'] = [];
            if (is_array($adminIdKeys)) {
                foreach ($adminIdKeys as $key) {
                    try {
                        $fd = $redis->get($key);
                        $ttl = $redis->ttl($key);
                        $debugInfo['admin_id_connections'][$key] = [
                            'fd' => $fd,
                            'ttl' => $ttl,
                            'exists' => $redis->exists($key)
                        ];
                        
                        if (empty($fd)) {
                            Log::warning("管理员id键 {$key} 值为空，TTL: {$ttl}");
                        }
                    } catch (\Exception $e) {
                        $debugInfo['admin_id_connections'][$key] = [
                            'error' => $e->getMessage()
                        ];
                    }
                }
            }

            // 尝试获取Room信息
            try {
                $handler = app(CombinedHandler::class);
                $adminGroupClients = $handler->room->getClients('admin_group');
                $debugInfo['room_info']['admin_group'] = $adminGroupClients;
                Log::info("admin_group房间成员: " . json_encode($adminGroupClients));
                
                // 获取所有房间信息
                $allRooms = [];
                $roomKeys = ['admin_group', 'user_group', 'kefu_group'];
                foreach ($roomKeys as $roomKey) {
                    try {
                        $clients = $handler->room->getClients($roomKey);
                        $allRooms[$roomKey] = $clients;
                    } catch (\Exception $e) {
                        $allRooms[$roomKey] = 'Error: ' . $e->getMessage();
                    }
                }
                $debugInfo['room_info']['all_rooms'] = $allRooms;
                
            } catch (\Exception $e) {
                $debugInfo['room_info']['error'] = $e->getMessage();
                Log::error("获取Room信息失败: " . $e->getMessage());
            }

            return JsonServer::success('调试信息获取成功', $debugInfo);

        } catch (\Exception $e) {
            Log::error('获取调试信息失败: ' . $e->getMessage());
            return JsonServer::error('获取调试信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 重建管理员连接信息
     * @ApiTitle (重建管理员连接)
     * @ApiSummary (手动重建管理员Redis连接信息)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"admin_id", "type":"integer", "require":true, "desc":"管理员ID"},
     *   {"name":"nickname", "type":"string", "require":false, "desc":"管理员昵称，默认为'管理员'"},
     *   {"name":"fd", "type":"integer", "require":true, "desc":"连接标识符"}
     * )
     * @ApiReturn ({"code":1,"msg":"重建成功","data":{}})
     */
    public function rebuildAdminConnection()
    {
        try {
            $admin_id = Request::param('admin_id', 0, 'intval');
            $nickname = Request::param('nickname', '管理员');
            $fd = Request::param('fd', 0, 'intval');

            if (empty($admin_id) || empty($fd)) {
                return JsonServer::error('管理员ID和连接标识符不能为空');
            }

            $redis = Cache::store('redis');
            $websocketPrefix = config('default.websocket_prefix', 'socket_');
            $adminPrefix = $websocketPrefix . 'admin_';

            // 重建fd对应的管理员信息
            $fd_key = $adminPrefix . 'fd_' . $fd;
            $admin_data = [
                'uid' => $admin_id,
                'nickname' => $nickname,
                'type' => 'admin',
                'fd' => $fd
            ];
            $redis->set($fd_key, json_encode($admin_data), 86400);

            // 重建管理员ID对应的fd
            $admin_key = $adminPrefix . 'admin_' . $admin_id;
            $redis->set($admin_key, $fd, 86400);

            Log::info("手动重建管理员连接: admin_id={$admin_id}, nickname={$nickname}, fd={$fd}");

            return JsonServer::success('管理员连接信息重建成功', [
                'admin_id' => $admin_id,
                'nickname' => $nickname,
                'fd' => $fd,
                'fd_key' => $fd_key,
                'admin_key' => $admin_key
            ]);

        } catch (\Exception $e) {
            Log::error('重建管理员连接失败: ' . $e->getMessage());
            return JsonServer::error('重建失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试Redis订阅推送
     * @ApiTitle (测试Redis订阅推送)
     * @ApiSummary (专门测试Redis发布订阅功能)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'Redis订阅测试'"},
     *   {"name":"content", "type":"string", "require":false, "desc":"通知内容，默认为'这是一条Redis订阅测试消息'"}
     * )
     * @ApiReturn ({"code":1,"msg":"推送成功","data":{}})
     */
    public function testDirectPush()
    {
        try {
            $title = Request::param('title', 'Redis订阅测试');
            $content = Request::param('content', '这是一条Redis订阅测试消息，时间: ' . date('H:i:s'));

            $redis = Cache::store('redis');
            
            // 构建通知数据
            $notificationData = [
                'type' => 'system',
                'title' => $title,
                'content' => $content,
                'url' => '',
                'icon' => 0,
                'timestamp' => time()
            ];

            // 构建符合NotificationListener期望的消息格式
            $wsMessage = [
                'event' => 'admin_notification',
                'data' => $notificationData
            ];

            $messageJson = json_encode($wsMessage, JSON_UNESCAPED_UNICODE);
            Log::info('发布Redis订阅消息: ' . $messageJson);

            // 发布到admin_notification频道
            $publishResult = $redis->publish('admin_notification', $messageJson);

            Log::info("Redis发布结果: {$publishResult}个订阅者收到消息");

            return JsonServer::success('Redis订阅测试完成', [
                'title' => $title,
                'content' => $content,
                'message_data' => $wsMessage,
                'publish_result' => $publishResult,
                'message' => $publishResult > 0 ? "成功发布给{$publishResult}个订阅者" : "发布失败，没有订阅者"
            ]);

        } catch (\Exception $e) {
            Log::error('Redis订阅测试失败: ' . $e->getMessage());
            return JsonServer::error('Redis订阅测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试Room推送
     * @ApiTitle (测试Room推送)
     * @ApiSummary (测试通过Room直接推送消息)
     * @ApiMethod (POST)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'Room推送测试'"},
     *   {"name":"content", "type":"string", "require":false, "desc":"通知内容，默认为'这是一条Room推送测试消息'"}
     * )
     * @ApiReturn ({"code":1,"msg":"推送成功","data":{}})
     */
    public function testRoomPush()
    {
        try {
            $title = Request::param('title', 'Room推送测试');
            $content = Request::param('content', '这是一条Room推送测试消息，时间: ' . date('H:i:s'));

            Log::info("测试Room推送: title={$title}, content={$content}");

            // 直接通过CombinedHandler推送
            $handler = app(CombinedHandler::class);
            $result = $handler->sendNotificationToAdmin($title, $content, 'system', '', 0);

            Log::info("Room推送结果: " . ($result ? '成功' : '失败'));

            return JsonServer::success('Room推送测试完成', [
                'title' => $title,
                'content' => $content,
                'result' => $result,
                'message' => $result ? 'Room推送成功' : 'Room推送失败'
            ]);

        } catch (\Exception $e) {
            Log::error('测试Room推送失败: ' . $e->getMessage());
            return JsonServer::error('Room推送测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查Redis订阅状态
     * @ApiTitle (检查Redis订阅状态)
     * @ApiSummary (检查Redis连接和订阅频道状态)
     * @ApiMethod (GET)
     * @ApiReturn ({"code":1,"msg":"检查完成","data":{}})
     */
    public function checkRedisSubscribe()
    {
        try {
            $redis = Cache::store('redis');
            
            $checkResult = [
                'redis_connection' => false,
                'redis_info' => [],
                'publish_test' => [],
                'swoole_config' => [],
                'timestamp' => time()
            ];

            // 1. 测试Redis连接
            try {
                $redis->ping();
                $checkResult['redis_connection'] = true;
                $checkResult['redis_info']['ping'] = 'PONG';
                
                // 获取Redis信息
                $info = $redis->info();
                if (is_array($info)) {
                    $checkResult['redis_info']['connected_clients'] = $info['connected_clients'] ?? 'unknown';
                    $checkResult['redis_info']['used_memory_human'] = $info['used_memory_human'] ?? 'unknown';
                }
                
            } catch (\Exception $e) {
                $checkResult['redis_info']['error'] = $e->getMessage();
            }

            // 2. 获取Swoole配置
            $swooleConfig = config('swoole.websocket');
            $checkResult['swoole_config'] = [
                'handler' => $swooleConfig['handler'] ?? 'null',
                'room_type' => $swooleConfig['room']['type'] ?? 'null',
                'redis_host' => $swooleConfig['room']['redis']['host'] ?? 'null',
                'subscribe_channels' => $swooleConfig['subscribe'] ?? []
            ];

            // 3. 测试发布消息
            $testMessage = [
                'event' => 'admin_notification',
                'data' => [
                    'type' => 'system',
                    'title' => 'Redis订阅检查',
                    'content' => '这是Redis订阅功能检查消息',
                    'timestamp' => time()
                ]
            ];

            $publishResult = $redis->publish('admin_notification', json_encode($testMessage));
            $checkResult['publish_test'] = [
                'channel' => 'admin_notification',
                'message' => $testMessage,
                'subscribers' => $publishResult,
                'success' => $publishResult > 0
            ];

            Log::info('Redis订阅状态检查: ' . json_encode($checkResult));

            return JsonServer::success('Redis订阅状态检查完成', $checkResult);

        } catch (\Exception $e) {
            Log::error('Redis订阅状态检查失败: ' . $e->getMessage());
            return JsonServer::error('检查失败: ' . $e->getMessage());
        }
    }

    /**
     * 通过WebSocket直接推送
     */
    private function pushViaWebSocket($title, $content, $type, $url, $icon)
    {
        try {
            // 方法1: 尝试通过CombinedHandler推送
            $handler = app(CombinedHandler::class);
            $result1 = $handler->sendNotificationToAdmin($title, $content, $type, $url, $icon);

            // 方法2: 直接通过Redis获取管理员连接并推送
            $result2 = $this->directPushToAdmins($title, $content, $type, $url, $icon);

            $success = $result1 || $result2['success'];

            return [
                'success' => $success,
                'method' => 'websocket_direct',
                'handler_result' => $result1,
                'direct_result' => $result2,
                'message' => $success ? '直接WebSocket推送成功' : '直接WebSocket推送失败'
            ];

        } catch (\Exception $e) {
            Log::error('WebSocket直接推送失败: ' . $e->getMessage());
            return [
                'success' => false,
                'method' => 'websocket_direct',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 直接推送给管理员连接
     */
    private function directPushToAdmins($title, $content, $type, $url, $icon)
    {
        try {
            $redis = Cache::store('redis');
            $websocketPrefix = config('default.websocket_prefix', 'socket_');
            $adminPrefix = $websocketPrefix . 'admin_';
            
            Log::info("直接推送给管理员 - websocket_prefix: {$websocketPrefix}, admin_prefix: {$adminPrefix}");
            
            // 构建通知数据
            $notificationData = [
                'type' => $type,
                'title' => $title,
                'content' => $content,
                'url' => $url,
                'icon' => $icon,
                'timestamp' => time()
            ];

            // 获取所有管理员连接
            $adminFds = [];
            $adminKeys = $redis->keys($adminPrefix . 'fd_*');
            Log::info("查找管理员fd键: " . json_encode($adminKeys));
            
            if (is_array($adminKeys)) {
                foreach ($adminKeys as $key) {
                    $adminData = $redis->get($key);
                    if ($adminData) {
                        $data = json_decode($adminData, true);
                        if ($data && isset($data['type']) && $data['type'] === 'admin' && isset($data['fd'])) {
                            $adminFds[] = $data['fd'];
                        }
                    }
                }
            }

            if (empty($adminFds)) {
                Log::warning("没有找到在线管理员连接");
                return [
                    'success' => false,
                    'admin_count' => 0,
                    'searched_keys' => $adminKeys,
                    'message' => '没有在线管理员'
                ];
            }

            Log::info("找到 " . count($adminFds) . " 个管理员连接: " . json_encode($adminFds));

            // 尝试通过Redis发布推送
            $successCount = 0;

            foreach ($adminFds as $fd) {
                try {
                    // 通过Redis发布一个特定的消息给特定的fd
                    $directMessage = [
                        'event' => 'notification',
                        'data' => $notificationData,
                        'target_fd' => $fd
                    ];
                    
                    // 发布到特殊频道，让Swoole服务器处理
                    $publishResult = $redis->publish('admin_direct_push', json_encode($directMessage));
                    Log::info("发布直接推送消息给fd {$fd}: " . ($publishResult > 0 ? '成功' : '失败'));
                    $successCount++;
                    
                } catch (\Exception $e) {
                    Log::error("推送给fd {$fd} 失败: " . $e->getMessage());
                }
            }

            return [
                'success' => $successCount > 0,
                'admin_count' => count($adminFds),
                'success_count' => $successCount,
                'admin_fds' => $adminFds,
                'searched_keys' => $adminKeys,
                'message' => "直接推送完成，成功 {$successCount}/" . count($adminFds)
            ];

        } catch (\Exception $e) {
            Log::error('直接推送管理员失败: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 通过Redis发布订阅推送
     */
    private function pushViaRedis($title, $content, $type, $url, $icon)
    {
        try {
            // 构建通知数据
            $notificationData = [
                'type' => $type,
                'title' => $title,
                'content' => $content,
                'url' => $url,
                'icon' => $icon,
                'timestamp' => time()
            ];

            // 构建符合NotificationListener期望的消息格式
            $wsMessage = [
                'event' => 'admin_notification',
                'data' => $notificationData
            ];

            $messageJson = json_encode($wsMessage, JSON_UNESCAPED_UNICODE);
            Log::info('通过Redis发布推送admin通知: ' . $messageJson);

            // 使用Redis发布消息
            $redis = Cache::store('redis');
            $publishResult = $redis->publish('admin_notification', $messageJson);

            $success = $publishResult > 0;
            Log::info("Redis发布到 'admin_notification' 频道: " . ($success ? "成功，{$publishResult}个订阅者收到" : "失败，没有订阅者"));

            // 如果没有订阅者，尝试直接通过WebSocket推送
            if (!$success) {
                Log::warning("Redis发布没有订阅者，尝试直接WebSocket推送");
                try {
                    $handler = app(CombinedHandler::class);
                    $directResult = $handler->sendNotificationToAdmin($title, $content, $type, $url, $icon);
                    
                    return [
                        'success' => $directResult,
                        'method' => 'redis_publish_fallback_websocket',
                        'subscribers' => $publishResult,
                        'fallback_result' => $directResult,
                        'message' => $directResult ? "Redis推送失败，WebSocket直推成功" : "Redis和WebSocket推送都失败"
                    ];
                } catch (\Exception $e) {
                    Log::error('WebSocket直推失败: ' . $e->getMessage());
                }
            }

            return [
                'success' => $success,
                'method' => 'redis_publish',
                'subscribers' => $publishResult,
                'message' => $success ? "Redis推送成功，{$publishResult}个订阅者收到" : "Redis推送失败，没有订阅者"
            ];

        } catch (\Exception $e) {
            Log::error('Redis推送失败: ' . $e->getMessage());
            return [
                'success' => false,
                'method' => 'redis_publish',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 测试Swoole服务器连接
     */
    private function testSwooleConnection()
    {
        try {
            $swooleHost = env('SWOOLE_HOST', '127.0.0.1');
            $swoolePort = env('SWOOLE_PORT', 20211);
            
            $client = new \Swoole\Client(SWOOLE_SOCK_TCP);
            $connected = $client->connect($swooleHost, $swoolePort, 1);
            
            if ($connected) {
                $client->close();
                return [
                    'status' => 'connected',
                    'host' => $swooleHost,
                    'port' => $swoolePort,
                    'message' => 'Swoole服务器连接正常'
                ];
            } else {
                return [
                    'status' => 'failed',
                    'host' => $swooleHost,
                    'port' => $swoolePort,
                    'error' => $client->errMsg,
                    'message' => 'Swoole服务器连接失败'
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
                'message' => 'Swoole服务器测试异常'
            ];
        }
    }

    /**
     * 测试Redis连接
     */
    private function testRedisConnection()
    {
        try {
            $redis = Cache::store('redis');
            $redis->ping();
            return [
                'status' => 'connected',
                'message' => 'Redis连接正常'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'message' => 'Redis连接失败'
            ];
        }
    }

    /**
     * 获取在线管理员数量
     */
    private function getOnlineAdminCount()
    {
        try {
            $redis = Cache::store('redis');
            $websocketPrefix = config('default.websocket_prefix', 'socket_');
            $adminPrefix = $websocketPrefix . 'admin_';
            
            Log::info("获取在线管理员数量 - websocket_prefix: {$websocketPrefix}, admin_prefix: {$adminPrefix}");
            
            $adminKeys = $redis->keys($adminPrefix . 'admin_*');
            Log::info("找到管理员键: " . json_encode($adminKeys));
            
            return is_array($adminKeys) ? count($adminKeys) : 0;
            
        } catch (\Exception $e) {
            Log::error('获取在线管理员数量失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取在线管理员列表
     */
    private function getOnlineAdminList()
    {
        try {
            $redis = Cache::store('redis');
            $websocketPrefix = config('default.websocket_prefix', 'socket_');
            $adminPrefix = $websocketPrefix . 'admin_';
            
            Log::info("获取在线管理员列表 - websocket_prefix: {$websocketPrefix}, admin_prefix: {$adminPrefix}");
            
            $admins = [];
            
            // 方法1: 通过fd_*键获取管理员信息
            $adminFdKeys = $redis->keys($adminPrefix . 'fd_*');
            Log::info("找到管理员fd键: " . json_encode($adminFdKeys));
            
            if (is_array($adminFdKeys)) {
                foreach ($adminFdKeys as $key) {
                    $adminData = $redis->get($key);
                    if ($adminData) {
                        $data = json_decode($adminData, true);
                        if ($data && isset($data['type']) && $data['type'] === 'admin') {
                            $admins[] = [
                                'admin_id' => $data['uid'] ?? 0,
                                'nickname' => $data['nickname'] ?? '未知管理员',
                                'fd' => $data['fd'] ?? 0,
                                'key' => $key,
                                'data' => $data,
                                'online_time' => time()
                            ];
                        }
                    }
                }
            }
            
            // 方法2: 通过admin_*键获取管理员信息
            $adminIdKeys = $redis->keys($adminPrefix . 'admin_*');
            Log::info("找到管理员id键: " . json_encode($adminIdKeys));
            
            $adminIds = [];
            if (is_array($adminIdKeys)) {
                foreach ($adminIdKeys as $key) {
                    $fd = $redis->get($key);
                    if ($fd) {
                        $adminId = str_replace($adminPrefix . 'admin_', '', $key);
                        $adminIds[] = [
                            'admin_id' => $adminId,
                            'fd' => $fd,
                            'key' => $key
                        ];
                    }
                }
            }
            
            // 方法3: 尝试通过Room获取admin_group成员
            $roomClients = [];
            try {
                $handler = app(CombinedHandler::class);
                $roomClients = $handler->room->getClients('admin_group');
                Log::info("admin_group房间成员: " . json_encode($roomClients));
            } catch (\Exception $e) {
                Log::error("获取admin_group房间成员失败: " . $e->getMessage());
                $roomClients = ['error' => $e->getMessage()];
            }
            
            return [
                'config' => [
                    'websocket_prefix' => $websocketPrefix,
                    'admin_prefix' => $adminPrefix
                ],
                'fd_method' => $admins,
                'id_method' => $adminIds,
                'room_method' => $roomClients,
                'total_count' => count($admins)
            ];
            
        } catch (\Exception $e) {
            Log::error('获取在线管理员列表失败: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
}