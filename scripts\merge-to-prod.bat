@echo off
chcp 65001 >nul
echo 开始合并到正式环境...

:: 获取当前分支
for /f "tokens=*" %%i in ('git branch --show-current') do set current_branch=%%i
echo 当前分支: %current_branch%

:: 切换到master分支
echo 切换到master分支...
git checkout master

:: 拉取最新的master分支
echo 拉取最新的master分支...
git pull origin master

:: 合并develop分支
echo 合并develop分支到master...
git merge develop

if %errorlevel% equ 0 (
    echo 合并成功！
    
    :: 推送到远程master分支
    echo 推送到远程master分支...
    git push origin master
    
    if %errorlevel% equ 0 (
        echo 推送成功！
        echo 正式环境需要手动部署，请登录服务器执行:
        echo bash /www/wwwroot/www.huohanghang.cn/scripts/deploy-prod.sh
        echo.
        echo 或者访问正式环境查看: https://www.huohanghang.cn
    ) else (
        echo 推送失败，请检查网络连接和权限
    )
) else (
    echo 合并失败，可能存在冲突，请手动解决
    git status
)

:: 切换回原来的分支
if not "%current_branch%"=="master" (
    echo 切换回原分支: %current_branch%
    git checkout %current_branch%
)

pause