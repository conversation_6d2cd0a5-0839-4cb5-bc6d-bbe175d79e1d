<?php
/**
 * Redis连接监控脚本
 */

echo "🔍 Redis连接监控\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 50) . "\n";

$redisConfig = [
    'host' => 'r-bp180wgq6voudt9d75.redis.rds.aliyuncs.com',
    'port' => 6379,
    'database' => 0
];

// 测试基本连接
echo "1. 测试基本Redis连接...\n";
try {
    $redis = new Redis();
    $startTime = microtime(true);
    
    if ($redis->connect($redisConfig['host'], $redisConfig['port'], 5)) {
        $connectTime = round((microtime(true) - $startTime) * 1000, 2);
        echo "✅ 连接成功 (耗时: {$connectTime}ms)\n";
        
        // 选择数据库
        if ($redis->select($redisConfig['database'])) {
            echo "✅ 数据库选择成功\n";
        } else {
            echo "❌ 数据库选择失败\n";
        }
        
        // 测试ping
        $pingResult = $redis->ping();
        echo "Ping结果: " . ($pingResult ? '✅ PONG' : '❌ 无响应') . "\n";
        
        // 获取Redis信息
        $info = $redis->info();
        if (is_array($info)) {
            echo "Redis版本: " . ($info['redis_version'] ?? '未知') . "\n";
            echo "连接客户端数: " . ($info['connected_clients'] ?? '未知') . "\n";
            echo "使用内存: " . ($info['used_memory_human'] ?? '未知') . "\n";
            echo "最大内存: " . ($info['maxmemory_human'] ?? '未知') . "\n";
        }
        
        $redis->close();
    } else {
        echo "❌ 连接失败\n";
    }
} catch (Exception $e) {
    echo "❌ 连接异常: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n";

// 测试发布订阅
echo "2. 测试发布订阅功能...\n";
try {
    // 发布者
    $publisher = new Redis();
    if ($publisher->connect($redisConfig['host'], $redisConfig['port'], 5)) {
        $publisher->select($redisConfig['database']);
        
        $testMessage = [
            'event' => 'admin_notification',
            'data' => [
                'type' => 'system',
                'title' => '连接测试',
                'content' => '这是Redis连接测试消息',
                'timestamp' => time()
            ]
        ];
        
        $publishResult = $publisher->publish('admin_notification', json_encode($testMessage));
        echo "发布测试消息: " . ($publishResult >= 0 ? "✅ 成功，{$publishResult}个订阅者" : "❌ 失败") . "\n";
        
        $publisher->close();
    } else {
        echo "❌ 发布者连接失败\n";
    }
} catch (Exception $e) {
    echo "❌ 发布订阅测试异常: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n";

// 网络连通性测试
echo "3. 网络连通性测试...\n";
$host = $redisConfig['host'];
$port = $redisConfig['port'];

// 使用fsockopen测试连接
$startTime = microtime(true);
$socket = @fsockopen($host, $port, $errno, $errstr, 5);
if ($socket) {
    $connectTime = round((microtime(true) - $startTime) * 1000, 2);
    echo "✅ TCP连接成功 (耗时: {$connectTime}ms)\n";
    fclose($socket);
} else {
    echo "❌ TCP连接失败: {$errstr} (错误码: {$errno})\n";
}

// DNS解析测试
echo "DNS解析测试: ";
$ip = gethostbyname($host);
if ($ip !== $host) {
    echo "✅ {$host} -> {$ip}\n";
} else {
    echo "❌ DNS解析失败\n";
}

echo "\n" . str_repeat("=", 50) . "\n";

// 建议
echo "📋 连接建议:\n";
echo "1. 如果连接经常中断，可能是网络不稳定\n";
echo "2. 检查阿里云Redis实例的连接数限制\n";
echo "3. 确认安全组规则允许连接\n";
echo "4. 考虑使用内网连接而非公网连接\n";
echo "5. 监控Redis实例的CPU和内存使用情况\n";

echo "\n✅ 监控完成\n";
?>