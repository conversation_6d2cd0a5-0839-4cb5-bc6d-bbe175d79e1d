<?php

namespace app\common\websocket;

use think\facade\Log;

/**
 * 简单的Redis轮询器
 * 作为Redis订阅的替代方案
 */
class SimpleRedisPoller
{
    protected $handler;
    protected $redis;
    protected $isRunning = false;
    
    public function __construct($handler)
    {
        $this->handler = $handler;
    }
    
    /**
     * 启动轮询
     */
    public function start()
    {
        if ($this->isRunning) {
            return;
        }
        
        $this->isRunning = true;
        
        // 使用Swoole定时器每秒检查一次
        \Swoole\Timer::tick(1000, function () {
            $this->poll();
        });
        
        error_log('Redis轮询器启动成功');
    }
    
    /**
     * 轮询检查Redis队列
     */
    protected function poll()
    {
        try {
            if (!$this->redis) {
                $this->connectRedis();
            }
            
            // 检查admin_notification队列
            $message = $this->redis->lpop('admin_notification_queue');
            if ($message) {
                $this->handleMessage($message);
            }
            
        } catch (\Exception $e) {
            error_log('Redis轮询异常: ' . $e->getMessage());
            $this->redis = null; // 重置连接，下次重新连接
        }
    }
    
    /**
     * 连接Redis
     */
    protected function connectRedis()
    {
        $this->redis = new \Redis();
        
        if (!$this->redis->connect('r-bp180wgq6voudt9d75.redis.rds.aliyuncs.com', 6379, 5)) {
            throw new \Exception('Redis连接失败');
        }
        
        $this->redis->select(0);
    }
    
    /**
     * 处理消息
     */
    protected function handleMessage($message)
    {
        try {
            $data = json_decode($message, true);
            if (!$data) {
                error_log('Redis轮询消息JSON解析失败: ' . $message);
                return;
            }
            
            error_log("Redis轮询收到消息: " . $message);
            
            if (isset($data['event']) && $data['event'] === 'admin_notification' && isset($data['data'])) {
                $this->handler->directPushToAdmins($data['data']);
            }
            
        } catch (\Exception $e) {
            error_log('Redis轮询消息处理失败: ' . $e->getMessage());
        }
    }
}