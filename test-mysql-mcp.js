const mysql = require('mysql2/promise');

async function testMySQLConnection() {
    const config = {
        host: 'rm-bp1y50i48aa95aw2beo.mysql.rds.aliyuncs.com',
        port: 3306,
        user: 'root',
        password: 'yT636^wQjehF2@dfs',
        connectTimeout: 10000,
        acquireTimeout: 10000,
        timeout: 10000
    };

    console.log('正在测试 MySQL 连接...');
    console.log('配置:', {
        host: config.host,
        port: config.port,
        user: config.user,
        password: '***隐藏***'
    });

    try {
        console.log('尝试连接到 MySQL...');
        const connection = await mysql.createConnection(config);
        console.log('✅ MySQL 连接成功!');
        
        console.log('测试查询数据库列表...');
        const [databases] = await connection.execute('SHOW DATABASES');
        console.log('✅ 数据库列表:', databases.map(db => Object.values(db)[0]));
        
        await connection.end();
        console.log('✅ 连接已关闭');
        
    } catch (error) {
        console.error('❌ MySQL 连接失败:');
        console.error('错误代码:', error.code);
        console.error('错误消息:', error.message);
        console.error('完整错误:', error);
    }
}

testMySQLConnection();