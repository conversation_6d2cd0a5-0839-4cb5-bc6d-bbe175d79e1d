<?php
/**
 * 综合推送测试脚本
 */

$apiBase = 'http://your-domain.com/api/admin_push';

function testAPI($endpoint, $data = null) {
    global $apiBase;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiBase . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

echo "🚀 综合推送测试\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";

// 1. 确保管理员连接已重建
echo "1. 重建管理员连接...\n";
$result = testAPI('/rebuildAdminConnection', [
    'admin_id' => 1,
    'nickname' => 'admin',
    'fd' => 1
]);

if ($result['data']['code'] == 1) {
    echo "✅ 管理员连接重建成功\n";
} else {
    echo "❌ 管理员连接重建失败\n";
    exit(1);
}

sleep(1);

// 2. 测试Room推送（最直接的方式）
echo "\n2. 测试Room推送...\n";
$result = testAPI('/testRoomPush', [
    'title' => 'Room推送测试',
    'content' => '这是Room推送测试消息 - ' . date('H:i:s')
]);

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    if ($data['result']) {
        echo "✅ Room推送成功！\n";
        echo "请检查后台是否收到消息\n";
    } else {
        echo "❌ Room推送失败\n";
    }
} else {
    echo "❌ Room推送测试失败: " . ($result['data']['msg'] ?? 'unknown') . "\n";
}

sleep(2);

// 3. 测试Redis推送
echo "\n3. 测试Redis推送...\n";
$result = testAPI('/pushMessage', [
    'title' => 'Redis推送测试',
    'content' => '这是Redis推送测试消息 - ' . date('H:i:s'),
    'type' => 'system',
    'method' => 'redis'
]);

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    if (isset($data['redis'])) {
        $redisResult = $data['redis'];
        if ($redisResult['success']) {
            echo "✅ Redis推送成功！订阅者: " . $redisResult['subscribers'] . "\n";
        } else {
            echo "❌ Redis推送失败: " . ($redisResult['message'] ?? 'unknown') . "\n";
            if (isset($redisResult['fallback_result']) && $redisResult['fallback_result']) {
                echo "✅ 但WebSocket回退推送成功！\n";
            }
        }
    }
} else {
    echo "❌ Redis推送测试失败: " . ($result['data']['msg'] ?? 'unknown') . "\n";
}

sleep(2);

// 4. 测试WebSocket推送
echo "\n4. 测试WebSocket推送...\n";
$result = testAPI('/pushMessage', [
    'title' => 'WebSocket推送测试',
    'content' => '这是WebSocket推送测试消息 - ' . date('H:i:s'),
    'type' => 'success',
    'method' => 'websocket'
]);

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    if (isset($data['websocket'])) {
        $wsResult = $data['websocket'];
        if ($wsResult['success']) {
            echo "✅ WebSocket推送成功！\n";
        } else {
            echo "❌ WebSocket推送失败\n";
        }
    }
} else {
    echo "❌ WebSocket推送测试失败: " . ($result['data']['msg'] ?? 'unknown') . "\n";
}

sleep(2);

// 5. 测试双重推送
echo "\n5. 测试双重推送...\n";
$result = testAPI('/pushMessage', [
    'title' => '双重推送测试',
    'content' => '这是双重推送测试消息 - ' . date('H:i:s'),
    'type' => 'warning',
    'method' => 'both'
]);

if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    
    $wsSuccess = isset($data['websocket']) && $data['websocket']['success'];
    $redisSuccess = isset($data['redis']) && $data['redis']['success'];
    
    if ($wsSuccess || $redisSuccess) {
        echo "✅ 双重推送部分成功！\n";
        echo "- WebSocket: " . ($wsSuccess ? '成功' : '失败') . "\n";
        echo "- Redis: " . ($redisSuccess ? '成功' : '失败') . "\n";
    } else {
        echo "❌ 双重推送完全失败\n";
    }
} else {
    echo "❌ 双重推送测试失败: " . ($result['data']['msg'] ?? 'unknown') . "\n";
}

sleep(2);

// 6. 最终状态检查
echo "\n6. 最终状态检查...\n";
$result = testAPI('/getOnlineAdmins');
if ($result['data']['code'] == 1) {
    $data = $result['data']['data'];
    $totalCount = $data['total_count'] ?? 0;
    echo "在线管理员数量: {$totalCount}\n";
    
    if ($totalCount > 0) {
        echo "✅ 管理员连接正常\n";
    } else {
        echo "❌ 没有检测到在线管理员\n";
    }
} else {
    echo "❌ 无法获取管理员状态\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎯 测试总结:\n";
echo "1. 如果Room推送成功，说明WebSocket连接正常\n";
echo "2. 如果Redis推送失败，可能需要重启Swoole服务器\n";
echo "3. 请检查后台管理员页面是否收到推送消息\n";
echo "4. 如果都失败，请检查Swoole服务器是否正常运行\n";
echo "\n✅ 综合测试完成！\n";
?>