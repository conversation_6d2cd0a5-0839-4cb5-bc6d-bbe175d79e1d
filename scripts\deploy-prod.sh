#!/bin/bash

# 正式环境部署脚本
# 服务器路径: /www/wwwroot/www.huohanghang.cn/server

echo "开始部署到正式环境..."

# 进入正式环境目录
cd /www/wwwroot/www.huohanghang.cn/server

# 备份当前版本
echo "备份当前版本..."
cp -r . ../backup/$(date +%Y%m%d_%H%M%S) 2>/dev/null || mkdir -p ../backup && cp -r . ../backup/$(date +%Y%m%d_%H%M%S)

# 拉取最新的master分支代码
echo "拉取最新代码..."
git fetch origin
git reset --hard origin/master

# 安装/更新依赖
echo "更新依赖..."
if [ -f "composer.json" ]; then
    composer install --no-dev --optimize-autoloader
fi

if [ -f "package.json" ]; then
    npm install --production
fi

# 设置权限
echo "设置文件权限..."
chown -R www:www .
chmod -R 755 .
chmod -R 777 runtime/ 2>/dev/null || true
chmod -R 777 public/uploads/ 2>/dev/null || true

# 清理缓存
echo "清理缓存..."
php think clear 2>/dev/null || true

echo "正式环境部署完成！"
echo "正式地址: https://www.huohanghang.cn"