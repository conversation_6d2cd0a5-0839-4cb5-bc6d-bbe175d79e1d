#!/bin/bash

echo "开始合并到正式环境..."

# 检查当前分支
current_branch=$(git branch --show-current)
echo "当前分支: $current_branch"

# 切换到master分支
echo "切换到master分支..."
git checkout master

# 拉取最新的master分支
echo "拉取最新的master分支..."
git pull origin master

# 合并develop分支
echo "合并develop分支到master..."
git merge develop

if [ $? -eq 0 ]; then
    echo "合并成功！"
    
    # 推送到远程master分支
    echo "推送到远程master分支..."
    git push origin master
    
    if [ $? -eq 0 ]; then
        echo "推送成功！"
        echo "正式环境需要手动部署，请登录服务器执行:"
        echo "bash /www/wwwroot/www.huohanghang.cn/scripts/deploy-prod.sh"
        echo ""
        echo "或者访问正式环境查看: https://www.huohanghang.cn"
    else
        echo "推送失败，请检查网络连接和权限"
    fi
else
    echo "合并失败，可能存在冲突，请手动解决"
    git status
fi

# 切换回原来的分支
if [ "$current_branch" != "master" ]; then
    echo "切换回原分支: $current_branch"
    git checkout $current_branch
fi